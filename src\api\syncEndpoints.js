const express = require('express');
const logger = require('../utils/logger');

/**
 * Crea endpoints de API para control manual de sincronización
 * @param {Object} syncService - Instancia del servicio de sincronización
 * @returns {express.Router} - Router con endpoints configurados
 */
function createSyncEndpoints(syncService) {
  const router = express.Router();

  /**
   * GET /api/sync/status
   * Obtiene el estado general del servicio
   */
  router.get('/status', async (req, res) => {
    try {
      const stats = await syncService.getThirdPartiesStats();
      
      res.json({
        success: true,
        data: {
          service: 'running',
          thirdParties: stats,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Error obteniendo estado:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * POST /api/sync/third-parties
   * Ejecuta sincronización manual de terceros
   */
  router.post('/third-parties', async (req, res) => {
    try {
      const { fullSync = false } = req.body;
      
      logger.info(`Sincronización manual solicitada (completa: ${fullSync})`);
      
      const result = await syncService.manualThirdPartiesSync(fullSync);
      
      res.json({
        success: true,
        data: result,
        message: `Sincronización completada: ${result.processed} procesados, ${result.errors} errores`
      });
    } catch (error) {
      logger.error('Error en sincronización manual:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * GET /api/sync/third-parties/stats
   * Obtiene estadísticas de sincronización de terceros
   */
  router.get('/third-parties/stats', async (req, res) => {
    try {
      const stats = await syncService.getThirdPartiesStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error obteniendo estadísticas:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  return router;
}

module.exports = createSyncEndpoints;
