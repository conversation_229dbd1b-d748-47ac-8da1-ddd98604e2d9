{"name": "node-firebird", "version": "1.1.9", "description": "Pure JavaScript and Asynchronous Firebird client for Node.js.", "keywords": ["firebird", "database", "rdbms", "sql"], "homepage": "https://github.com/hgourvest/node-firebird", "repository": {"type": "git", "url": "https://github.com/hgourvest/node-firebird"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "main": "./lib", "types": "./lib/index.d.ts", "license": "MPL-2.0", "scripts": {"test": "mocha"}, "dependencies": {"big-integer": "^1.6.51", "long": "^5.2.3"}, "devDependencies": {"coveralls-next": "^4.2.0", "mocha": "^10.2.0", "nyc": "^15.0.1"}}