name: CI

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:        
        node: [18, 20]
        firebird-version: ['v3']

    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 10

    - name: Setup FirebirdSQL container
      uses: juarezr/firebirdsql-github-action@v1.2.0
      with:
        version: ${{ matrix.firebird-version }}
        isc_password: "masterkey"
        enable_legacy_client_auth: "true"        

    - name: Use Node.js ${{ matrix.node }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node }}


    - name: Build
      shell: bash
      run: |
        npm ci         

    - name: Test (Linux)      
      run: |
        export FIREBIRD_DATA=/firebird/data
        npx nyc npm test
