{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.568Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.571Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.609Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.614Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.614Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.647Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.647Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.647Z"}
{"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:08:25.284Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:08:25.285Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura SEA3464: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:08:26.196Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura aprobada: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:08:26.196Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:09:12.037Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:09:12.038Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.156Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.159Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.196Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.201Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.201Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.234Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.235Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.235Z"}
{"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:54.558Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:54.559Z"}
{"data":[{"field":"E","length":"N/A","value":1},{"field":"S","length":"N/A","value":1},{"field":"TIPO","length":3,"value":"FIA"},{"field":"BATCH","length":"N/A","value":18134},{"field":"ID_N","length":11,"value":"*********-4"},{"field":"FECHA","length":"N/A","value":"2025-05-13T00:00:00.000Z"},{"field":"TOTAL","length":"N/A","value":9565604.91},{"field":"USERNAME","length":6,"value":"SYSTEM"},{"field":"FECHA_HORA","length":20,"value":"20/6/2025, 7:12:55 a"},{"field":"OBSERV","length":31,"value":"Factura SEA3464 - GRUPO SAI SAS"},{"field":"BANCO","length":0,"value":""},{"field":"CHEQUE","length":0,"value":""},{"field":"DUEDATE","length":"N/A","value":"2025-05-13T00:00:00.000Z"},{"field":"LETRAS","length":104,"value":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE"},{"field":"IDVEND","length":"N/A","value":1},{"field":"SHIPTO","length":"N/A","value":0},{"field":"EXPORTADA","length":1,"value":"N"},{"field":"ENTREGADO","length":1,"value":"N"},{"field":"REVISADO","length":1,"value":"N"},{"field":"REVISOR","length":"N/A","value":null},{"field":"FECHA_REVISION","length":"N/A","value":null},{"field":"IMPRESO","length":1,"value":"N"},{"field":"DOC_FISICO","length":"N/A","value":null},{"field":"CHEQUE_POSTF","length":5,"value":"false"},{"field":"FECHA_CHEQUE","length":"N/A","value":null},{"field":"PROYECTO","length":"N/A","value":null},{"field":"SALDO_DEUDA","length":"N/A","value":null},{"field":"SALDO_DEUDA_ABONO","length":"N/A","value":null},{"field":"PONUMBER","length":"N/A","value":null},{"field":"INTERES_IMPLICITO","length":1,"value":"N"},{"field":"DETALLE","length":0,"value":""},{"field":"FECHA_CONTAB_CONSIG","length":1,"value":"N"},{"field":"DETERIORO_ESFA","length":1,"value":"N"},{"field":"CONCEPTO_NOTAFE","length":"N/A","value":null},{"field":"ENVIADO","length":1,"value":"N"},{"field":"CUFE","length":"N/A","value":null},{"field":"SUBTOTAL","length":"N/A","value":8038323.45},{"field":"SALESTAX","length":"N/A","value":1527281.46},{"field":"IMPCONSUMO","length":"N/A","value":0},{"field":"TOTAL_REAL","length":"N/A","value":9565604.91},{"field":"FECHA_RESPUESTA_DIAN","length":"N/A","value":null},{"field":"ID_BINARIO","length":"N/A","value":null},{"field":"SIN_CRUCE","length":1,"value":"N"},{"field":"CUDS","length":"N/A","value":null},{"field":"COD_OPERACION","length":"N/A","value":null},{"field":"FORMAPAGO","length":"N/A","value":null},{"field":"ID_RES","length":"N/A","value":0},{"field":"FECHA_INICIO_PERIODO","length":"N/A","value":null},{"field":"FECHA_FIN_PERIODO","length":"N/A","value":null}],"error":"Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')","level":"error","message":"Error insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T12:12:55.213Z"}
{"gdscode":*********,"gdsparams":["FK_CARPROEN_CUST","CARPROEN"],"level":"error","message":"Error procesando factura SEA3464: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')","service":"supabase-firebird-sync","stack":"Error: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:12:55.217Z"}
{"gdscode":*********,"gdsparams":["FK_CARPROEN_CUST","CARPROEN"],"level":"error","message":"Error procesando factura aprobada: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')","service":"supabase-firebird-sync","stack":"Error: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:12:55.217Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:18:33.183Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:18:33.184Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.750Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.753Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.813Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.818Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.818Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.866Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.866Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.867Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:01.134Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:01.136Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.186Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.189Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.229Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.235Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.235Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.269Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.269Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.269Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:07.066Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:07.068Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.302Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.305Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.343Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.349Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.350Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.383Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.383Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.383Z"}
{"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:39.015Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:39.016Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.271Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.272Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.272Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.272Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.272Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.277Z"}
{"level":"info","message":"NIT principal corregido: ********* -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.277Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.277Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.278Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.278Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.278Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.278Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.283Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.283Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.287Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.291Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.292Z"}
{"BANCO":"","BATCH":18134,"CHEQUE":"","CHEQUE_POSTF":"false","COD_OPERACION":null,"CONCEPTO_NOTAFE":null,"CUDS":null,"CUFE":null,"DETALLE":"","DETERIORO_ESFA":"N","DOC_FISICO":null,"DUEDATE":"2025-05-13T00:00:00.000Z","E":1,"ENTREGADO":"N","ENVIADO":"N","EXPORTADA":"N","FECHA":"2025-05-13T00:00:00.000Z","FECHA_CHEQUE":null,"FECHA_CONTAB_CONSIG":"N","FECHA_FIN_PERIODO":null,"FECHA_HORA":"20/6/2025, 8:19:40 a","FECHA_INICIO_PERIODO":null,"FECHA_RESPUESTA_DIAN":null,"FECHA_REVISION":null,"FORMAPAGO":null,"IDVEND":1,"ID_BINARIO":null,"ID_N":"*********","ID_RES":0,"IMPCONSUMO":0,"IMPRESO":"N","INTERES_IMPLICITO":"N","LETRAS":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE","OBSERV":"Factura SEA3464 - GRUPO SAI SAS","PONUMBER":null,"PROYECTO":null,"REVISADO":"N","REVISOR":null,"S":1,"SALDO_DEUDA":null,"SALDO_DEUDA_ABONO":null,"SALESTAX":1527281.46,"SHIPTO":0,"SIN_CRUCE":"N","SUBTOTAL":8038323.45,"TIPO":"FIA","TOTAL":9565604.91,"TOTAL_REAL":9565604.91,"USERNAME":"SYSTEM","level":"debug","message":"Datos mapeados para CARPROEN:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.591Z"}
{"level":"debug","message":"Datos mapeados para CARPRODE (3 registros)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.592Z"}
{"level":"debug","message":"Insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:19:40.593Z","values":[1,1,"FIA",18134,"*********","2025-05-13T00:00:00.000Z",9565604.91,"SYSTEM","20/6/2025, 8:19:40 a","Factura SEA3464 - GRUPO SAI SAS","","","2025-05-13T00:00:00.000Z","NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE",1,0,"N","N","N",null,null,"N",null,"false",null,null,null,null,null,"N","","N","N",null,"N",null,8038323.45,1527281.46,0,9565604.91,null,null,"N",null,null,null,0,null,null]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:19:40.596Z","values":["FIA",18134,"*********",51950501,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de la comisión sobre ventas CS según fact",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-8038323.45,0,0,0,0,8038323.45,8038323.45,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"data":[{"field":"TIPO","length":3,"value":"FIA"},{"field":"BATCH","length":"N/A","value":18134},{"field":"ID_N","length":9,"value":"*********"},{"field":"ACCT","length":"N/A","value":51950501},{"field":"E","length":"N/A","value":1},{"field":"S","length":"N/A","value":1},{"field":"CRUCE","length":0,"value":""},{"field":"INVC","length":7,"value":"SEA3464"},{"field":"FECHA","length":"N/A","value":"2025-06-06T00:00:00.000Z"},{"field":"DUEDATE","length":"N/A","value":"2025-06-06T00:00:00.000Z"},{"field":"DPTO","length":"N/A","value":0},{"field":"CCOST","length":"N/A","value":0},{"field":"ACTIVIDAD","length":0,"value":""},{"field":"DESCRIPCION","length":50,"value":"Registro de la comisión sobre ventas CS según fact"},{"field":"DIAS","length":"N/A","value":0},{"field":"DESTINO","length":"N/A","value":1},{"field":"TIPO_REF","length":"N/A","value":null},{"field":"REFERENCIA","length":"N/A","value":null},{"field":"TIPO_IMP","length":0,"value":""},{"field":"NRO_IMP","length":"N/A","value":0},{"field":"CONCEPTO_IMP","length":"N/A","value":0},{"field":"BANCO","length":"N/A","value":null},{"field":"CHEQUE","length":"N/A","value":null},{"field":"PROYECTO","length":0,"value":""},{"field":"CONCEPTO_PAGO","length":"N/A","value":null},{"field":"ID_TIPOCARTERA","length":"N/A","value":null},{"field":"INVC_ENTERO","length":"N/A","value":0},{"field":"CHEQUE_POSTF","length":5,"value":"false"},{"field":"FECHA_CHEQUE","length":"N/A","value":null},{"field":"SALDO","length":"N/A","value":-8038323.45},{"field":"CREDIT","length":"N/A","value":0},{"field":"TASA_CAMBIO","length":"N/A","value":0},{"field":"CREDITO_US","length":"N/A","value":0},{"field":"DEBITO_US","length":"N/A","value":0},{"field":"BASE","length":"N/A","value":8038323.45},{"field":"DEBIT","length":"N/A","value":8038323.45},{"field":"CUOTA","length":"N/A","value":1},{"field":"FECHA_CONSIG","length":"N/A","value":null},{"field":"FECHA_FACTURA","length":"N/A","value":"2025-05-13T00:00:00.000Z"},{"field":"MAYOR_VALOR","length":"N/A","value":null},{"field":"VALOR_IMPUESTO","length":"N/A","value":null},{"field":"IMPORT","length":1,"value":"N"},{"field":"COD_FLUJOEFE","length":"N/A","value":0},{"field":"IDVEND","length":"N/A","value":null},{"field":"PORC_TASA","length":"N/A","value":19},{"field":"TIEMPO_MESES","length":"N/A","value":null},{"field":"PAGO_DISP","length":1,"value":"N"},{"field":"REGISTROELECT","length":1,"value":"N"},{"field":"PORC_RETENCION","length":"N/A","value":null},{"field":"BASE_ELECTRONICA","length":"N/A","value":0}],"error":"Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","level":"error","message":"Error insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:19:40.634Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura SEA3464: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T13:19:40.670Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura aprobada: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T13:19:40.670Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.612Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.616Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.654Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.660Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.660Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.694Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.694Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.694Z"}
{"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:47.559Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:47.560Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.147Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.147Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.147Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.147Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.148Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.152Z"}
{"level":"info","message":"NIT principal corregido: ********* -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.152Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.152Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.153Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.153Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.153Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.153Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.156Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.156Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.156Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.157Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.157Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.157Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.157Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.160Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.160Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.162Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.163Z"}
{"BANCO":"","BATCH":18134,"CHEQUE":"","CHEQUE_POSTF":"false","COD_OPERACION":null,"CONCEPTO_NOTAFE":null,"CUDS":null,"CUFE":null,"DETALLE":"","DETERIORO_ESFA":"N","DOC_FISICO":null,"DUEDATE":"2025-05-13T00:00:00.000Z","E":1,"ENTREGADO":"N","ENVIADO":"N","EXPORTADA":"N","FECHA":"2025-05-13T00:00:00.000Z","FECHA_CHEQUE":null,"FECHA_CONTAB_CONSIG":"N","FECHA_FIN_PERIODO":null,"FECHA_HORA":"20/6/2025, 8:36:48 a","FECHA_INICIO_PERIODO":null,"FECHA_RESPUESTA_DIAN":null,"FECHA_REVISION":null,"FORMAPAGO":null,"IDVEND":1,"ID_BINARIO":null,"ID_N":"*********","ID_RES":0,"IMPCONSUMO":0,"IMPRESO":"N","INTERES_IMPLICITO":"N","LETRAS":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE","OBSERV":"Factura SEA3464 - GRUPO SAI SAS","PONUMBER":null,"PROYECTO":null,"REVISADO":"N","REVISOR":null,"S":1,"SALDO_DEUDA":null,"SALDO_DEUDA_ABONO":null,"SALESTAX":1527281.46,"SHIPTO":0,"SIN_CRUCE":"N","SUBTOTAL":8038323.45,"TIPO":"FIA","TOTAL":9565604.91,"TOTAL_REAL":9565604.91,"USERNAME":"SYSTEM","level":"debug","message":"Datos mapeados para CARPROEN:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.198Z"}
{"level":"debug","message":"Datos mapeados para CARPRODE (3 registros)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.198Z"}
{"level":"debug","message":"Insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:36:48.200Z","values":[1,1,"FIA",18134,"*********","2025-05-13T00:00:00.000Z",9565604.91,"SYSTEM","20/6/2025, 8:36:48 a","Factura SEA3464 - GRUPO SAI SAS","","","2025-05-13T00:00:00.000Z","NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE",1,0,"N","N","N",null,null,"N",null,"false",null,null,null,null,null,"N","","N","N",null,"N",null,8038323.45,1527281.46,0,9565604.91,null,null,"N",null,null,null,0,null,null]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:36:48.202Z","values":["FIA",18134,"*********",51950501,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de la comisión sobre ventas CS ",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-8038323.45,0,0,0,0,8038323.45,8038323.45,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:36:48.273Z","values":["FIA",18134,"*********",24080219,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de IVA descontable por comisión",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-1527281.46,0,0,0,0,8038323.45,1527281.46,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:36:48.275Z","values":["FIA",18134,"*********",22050101,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de cuenta por pagar a GRUPO SAI",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,9565604.91,9565604.91,0,0,0,8038323.45,0,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,null,null,"N","N",null,0]}
{"level":"info","message":"Consecutivo actualizado a 18134 para tipo FIA","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.283Z"}
{"level":"info","message":"Factura SEA3464 procesada exitosamente con batch 18134","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.283Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:38:05.010Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:38:05.011Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.255Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.258Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.295Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.301Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.302Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.334Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.334Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.335Z"}
{"estado":"APROBADO","id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","service_response":null,"timestamp":"2025-06-20T13:55:40.672Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:40.673Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.257Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.257Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.257Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.257Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.258Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.261Z"}
{"level":"info","message":"NIT principal corregido: ********* -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.263Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.268Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.268Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.271Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.273Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.276Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.277Z"}
{"BANCO":"","BATCH":18135,"CHEQUE":"","CHEQUE_POSTF":"false","COD_OPERACION":null,"CONCEPTO_NOTAFE":null,"CUDS":null,"CUFE":null,"DETALLE":"","DETERIORO_ESFA":"N","DOC_FISICO":null,"DUEDATE":"2025-05-13T00:00:00.000Z","E":1,"ENTREGADO":"N","ENVIADO":"N","EXPORTADA":"N","FECHA":"2025-05-13T00:00:00.000Z","FECHA_CHEQUE":null,"FECHA_CONTAB_CONSIG":"N","FECHA_FIN_PERIODO":null,"FECHA_HORA":"20/6/2025, 8:55:41 a","FECHA_INICIO_PERIODO":null,"FECHA_RESPUESTA_DIAN":null,"FECHA_REVISION":null,"FORMAPAGO":null,"IDVEND":1,"ID_BINARIO":null,"ID_N":"*********","ID_RES":0,"IMPCONSUMO":0,"IMPRESO":"N","INTERES_IMPLICITO":"N","LETRAS":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE","OBSERV":"Factura SEA3464 - GRUPO SAI SAS","PONUMBER":null,"PROYECTO":null,"REVISADO":"N","REVISOR":null,"S":1,"SALDO_DEUDA":null,"SALDO_DEUDA_ABONO":null,"SALESTAX":1527281.46,"SHIPTO":0,"SIN_CRUCE":"N","SUBTOTAL":8038323.45,"TIPO":"FIA","TOTAL":9565604.91,"TOTAL_REAL":9565604.91,"USERNAME":"SYSTEM","level":"debug","message":"Datos mapeados para CARPROEN:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.314Z"}
{"level":"debug","message":"Datos mapeados para CARPRODE (3 registros)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.315Z"}
{"level":"debug","message":"Insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:55:41.317Z","values":[1,1,"FIA",18135,"*********","2025-05-13T00:00:00.000Z",9565604.91,"SYSTEM","20/6/2025, 8:55:41 a","Factura SEA3464 - GRUPO SAI SAS","","","2025-05-13T00:00:00.000Z","NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE",1,0,"N","N","N",null,null,"N",null,"false",null,null,null,null,null,"N","","N","N",null,"N",null,8038323.45,1527281.46,0,9565604.91,null,null,"N",null,null,null,0,null,null]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:55:41.319Z","values":["FIA",18135,"*********",51950501,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de la comisión sobre ventas CS ",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-8038323.45,0,0,0,0,8038323.45,8038323.45,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:55:41.322Z","values":["FIA",18135,"*********",24080219,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de IVA descontable por comisión",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-1527281.46,0,0,0,0,8038323.45,1527281.46,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:55:41.323Z","values":["FIA",18135,"*********",22050101,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de cuenta por pagar a GRUPO SAI",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,9565604.91,9565604.91,0,0,0,8038323.45,0,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,null,null,"N","N",null,0]}
{"level":"info","message":"Consecutivo actualizado a 18135 para tipo FIA","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.332Z"}
{"estado":"SINCRONIZADO","level":"info","message":"Estado de factura 85 actualizado:","service":"supabase-firebird-sync","serviceResponse":"Ok","timestamp":"2025-06-20T13:55:41.529Z"}
{"level":"info","message":"Factura SEA3464 procesada exitosamente con batch 18135","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.529Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:27.309Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:27.311Z"}
