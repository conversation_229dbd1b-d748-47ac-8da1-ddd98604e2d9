{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.568Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.571Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.609Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.614Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.614Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.647Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.647Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:07:04.647Z"}
{"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:08:25.284Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:08:25.285Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura SEA3464: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:08:26.196Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura aprobada: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:08:26.196Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:09:12.037Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:09:12.038Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.156Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.159Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.196Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.201Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.201Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.234Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.235Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:47.235Z"}
{"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:54.558Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:12:54.559Z"}
{"data":[{"field":"E","length":"N/A","value":1},{"field":"S","length":"N/A","value":1},{"field":"TIPO","length":3,"value":"FIA"},{"field":"BATCH","length":"N/A","value":18134},{"field":"ID_N","length":11,"value":"*********-4"},{"field":"FECHA","length":"N/A","value":"2025-05-13T00:00:00.000Z"},{"field":"TOTAL","length":"N/A","value":9565604.91},{"field":"USERNAME","length":6,"value":"SYSTEM"},{"field":"FECHA_HORA","length":20,"value":"20/6/2025, 7:12:55 a"},{"field":"OBSERV","length":31,"value":"Factura SEA3464 - GRUPO SAI SAS"},{"field":"BANCO","length":0,"value":""},{"field":"CHEQUE","length":0,"value":""},{"field":"DUEDATE","length":"N/A","value":"2025-05-13T00:00:00.000Z"},{"field":"LETRAS","length":104,"value":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE"},{"field":"IDVEND","length":"N/A","value":1},{"field":"SHIPTO","length":"N/A","value":0},{"field":"EXPORTADA","length":1,"value":"N"},{"field":"ENTREGADO","length":1,"value":"N"},{"field":"REVISADO","length":1,"value":"N"},{"field":"REVISOR","length":"N/A","value":null},{"field":"FECHA_REVISION","length":"N/A","value":null},{"field":"IMPRESO","length":1,"value":"N"},{"field":"DOC_FISICO","length":"N/A","value":null},{"field":"CHEQUE_POSTF","length":5,"value":"false"},{"field":"FECHA_CHEQUE","length":"N/A","value":null},{"field":"PROYECTO","length":"N/A","value":null},{"field":"SALDO_DEUDA","length":"N/A","value":null},{"field":"SALDO_DEUDA_ABONO","length":"N/A","value":null},{"field":"PONUMBER","length":"N/A","value":null},{"field":"INTERES_IMPLICITO","length":1,"value":"N"},{"field":"DETALLE","length":0,"value":""},{"field":"FECHA_CONTAB_CONSIG","length":1,"value":"N"},{"field":"DETERIORO_ESFA","length":1,"value":"N"},{"field":"CONCEPTO_NOTAFE","length":"N/A","value":null},{"field":"ENVIADO","length":1,"value":"N"},{"field":"CUFE","length":"N/A","value":null},{"field":"SUBTOTAL","length":"N/A","value":8038323.45},{"field":"SALESTAX","length":"N/A","value":1527281.46},{"field":"IMPCONSUMO","length":"N/A","value":0},{"field":"TOTAL_REAL","length":"N/A","value":9565604.91},{"field":"FECHA_RESPUESTA_DIAN","length":"N/A","value":null},{"field":"ID_BINARIO","length":"N/A","value":null},{"field":"SIN_CRUCE","length":1,"value":"N"},{"field":"CUDS","length":"N/A","value":null},{"field":"COD_OPERACION","length":"N/A","value":null},{"field":"FORMAPAGO","length":"N/A","value":null},{"field":"ID_RES","length":"N/A","value":0},{"field":"FECHA_INICIO_PERIODO","length":"N/A","value":null},{"field":"FECHA_FIN_PERIODO","length":"N/A","value":null}],"error":"Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')","level":"error","message":"Error insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T12:12:55.213Z"}
{"gdscode":*********,"gdsparams":["FK_CARPROEN_CUST","CARPROEN"],"level":"error","message":"Error procesando factura SEA3464: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')","service":"supabase-firebird-sync","stack":"Error: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:12:55.217Z"}
{"gdscode":*********,"gdsparams":["FK_CARPROEN_CUST","CARPROEN"],"level":"error","message":"Error procesando factura aprobada: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')","service":"supabase-firebird-sync","stack":"Error: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:12:55.217Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:18:33.183Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T12:18:33.184Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.750Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.753Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.813Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.818Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.818Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.866Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.866Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:15:50.867Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:01.134Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:01.136Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.186Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.189Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.229Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.235Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.235Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.269Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.269Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:18:39.269Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:07.066Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:07.068Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.302Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.305Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.343Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.349Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.350Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.383Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.383Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:24.383Z"}
{"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:39.015Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:39.016Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.271Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.272Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.272Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.272Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.272Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.277Z"}
{"level":"info","message":"NIT principal corregido: ********* -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.277Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.277Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.278Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.278Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.278Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.278Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.282Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.283Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.283Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.286Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.287Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.291Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.292Z"}
{"BANCO":"","BATCH":18134,"CHEQUE":"","CHEQUE_POSTF":"false","COD_OPERACION":null,"CONCEPTO_NOTAFE":null,"CUDS":null,"CUFE":null,"DETALLE":"","DETERIORO_ESFA":"N","DOC_FISICO":null,"DUEDATE":"2025-05-13T00:00:00.000Z","E":1,"ENTREGADO":"N","ENVIADO":"N","EXPORTADA":"N","FECHA":"2025-05-13T00:00:00.000Z","FECHA_CHEQUE":null,"FECHA_CONTAB_CONSIG":"N","FECHA_FIN_PERIODO":null,"FECHA_HORA":"20/6/2025, 8:19:40 a","FECHA_INICIO_PERIODO":null,"FECHA_RESPUESTA_DIAN":null,"FECHA_REVISION":null,"FORMAPAGO":null,"IDVEND":1,"ID_BINARIO":null,"ID_N":"*********","ID_RES":0,"IMPCONSUMO":0,"IMPRESO":"N","INTERES_IMPLICITO":"N","LETRAS":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE","OBSERV":"Factura SEA3464 - GRUPO SAI SAS","PONUMBER":null,"PROYECTO":null,"REVISADO":"N","REVISOR":null,"S":1,"SALDO_DEUDA":null,"SALDO_DEUDA_ABONO":null,"SALESTAX":1527281.46,"SHIPTO":0,"SIN_CRUCE":"N","SUBTOTAL":8038323.45,"TIPO":"FIA","TOTAL":9565604.91,"TOTAL_REAL":9565604.91,"USERNAME":"SYSTEM","level":"debug","message":"Datos mapeados para CARPROEN:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.591Z"}
{"level":"debug","message":"Datos mapeados para CARPRODE (3 registros)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:19:40.592Z"}
{"level":"debug","message":"Insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:19:40.593Z","values":[1,1,"FIA",18134,"*********","2025-05-13T00:00:00.000Z",9565604.91,"SYSTEM","20/6/2025, 8:19:40 a","Factura SEA3464 - GRUPO SAI SAS","","","2025-05-13T00:00:00.000Z","NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE",1,0,"N","N","N",null,null,"N",null,"false",null,null,null,null,null,"N","","N","N",null,"N",null,8038323.45,1527281.46,0,9565604.91,null,null,"N",null,null,null,0,null,null]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:19:40.596Z","values":["FIA",18134,"*********",51950501,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de la comisión sobre ventas CS según fact",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-8038323.45,0,0,0,0,8038323.45,8038323.45,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"data":[{"field":"TIPO","length":3,"value":"FIA"},{"field":"BATCH","length":"N/A","value":18134},{"field":"ID_N","length":9,"value":"*********"},{"field":"ACCT","length":"N/A","value":51950501},{"field":"E","length":"N/A","value":1},{"field":"S","length":"N/A","value":1},{"field":"CRUCE","length":0,"value":""},{"field":"INVC","length":7,"value":"SEA3464"},{"field":"FECHA","length":"N/A","value":"2025-06-06T00:00:00.000Z"},{"field":"DUEDATE","length":"N/A","value":"2025-06-06T00:00:00.000Z"},{"field":"DPTO","length":"N/A","value":0},{"field":"CCOST","length":"N/A","value":0},{"field":"ACTIVIDAD","length":0,"value":""},{"field":"DESCRIPCION","length":50,"value":"Registro de la comisión sobre ventas CS según fact"},{"field":"DIAS","length":"N/A","value":0},{"field":"DESTINO","length":"N/A","value":1},{"field":"TIPO_REF","length":"N/A","value":null},{"field":"REFERENCIA","length":"N/A","value":null},{"field":"TIPO_IMP","length":0,"value":""},{"field":"NRO_IMP","length":"N/A","value":0},{"field":"CONCEPTO_IMP","length":"N/A","value":0},{"field":"BANCO","length":"N/A","value":null},{"field":"CHEQUE","length":"N/A","value":null},{"field":"PROYECTO","length":0,"value":""},{"field":"CONCEPTO_PAGO","length":"N/A","value":null},{"field":"ID_TIPOCARTERA","length":"N/A","value":null},{"field":"INVC_ENTERO","length":"N/A","value":0},{"field":"CHEQUE_POSTF","length":5,"value":"false"},{"field":"FECHA_CHEQUE","length":"N/A","value":null},{"field":"SALDO","length":"N/A","value":-8038323.45},{"field":"CREDIT","length":"N/A","value":0},{"field":"TASA_CAMBIO","length":"N/A","value":0},{"field":"CREDITO_US","length":"N/A","value":0},{"field":"DEBITO_US","length":"N/A","value":0},{"field":"BASE","length":"N/A","value":8038323.45},{"field":"DEBIT","length":"N/A","value":8038323.45},{"field":"CUOTA","length":"N/A","value":1},{"field":"FECHA_CONSIG","length":"N/A","value":null},{"field":"FECHA_FACTURA","length":"N/A","value":"2025-05-13T00:00:00.000Z"},{"field":"MAYOR_VALOR","length":"N/A","value":null},{"field":"VALOR_IMPUESTO","length":"N/A","value":null},{"field":"IMPORT","length":1,"value":"N"},{"field":"COD_FLUJOEFE","length":"N/A","value":0},{"field":"IDVEND","length":"N/A","value":null},{"field":"PORC_TASA","length":"N/A","value":19},{"field":"TIEMPO_MESES","length":"N/A","value":null},{"field":"PAGO_DISP","length":1,"value":"N"},{"field":"REGISTROELECT","length":1,"value":"N"},{"field":"PORC_RETENCION","length":"N/A","value":null},{"field":"BASE_ELECTRONICA","length":"N/A","value":0}],"error":"Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","level":"error","message":"Error insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:19:40.634Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura SEA3464: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T13:19:40.670Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura aprobada: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T13:19:40.670Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.612Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.616Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.654Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.660Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.660Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.694Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.694Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:27:03.694Z"}
{"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:47.559Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:47.560Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.147Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.147Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.147Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.147Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.148Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.152Z"}
{"level":"info","message":"NIT principal corregido: ********* -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.152Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.152Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.153Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.153Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.153Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.153Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.156Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.156Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.156Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.157Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.157Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.157Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.157Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.159Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.160Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.160Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.162Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.163Z"}
{"BANCO":"","BATCH":18134,"CHEQUE":"","CHEQUE_POSTF":"false","COD_OPERACION":null,"CONCEPTO_NOTAFE":null,"CUDS":null,"CUFE":null,"DETALLE":"","DETERIORO_ESFA":"N","DOC_FISICO":null,"DUEDATE":"2025-05-13T00:00:00.000Z","E":1,"ENTREGADO":"N","ENVIADO":"N","EXPORTADA":"N","FECHA":"2025-05-13T00:00:00.000Z","FECHA_CHEQUE":null,"FECHA_CONTAB_CONSIG":"N","FECHA_FIN_PERIODO":null,"FECHA_HORA":"20/6/2025, 8:36:48 a","FECHA_INICIO_PERIODO":null,"FECHA_RESPUESTA_DIAN":null,"FECHA_REVISION":null,"FORMAPAGO":null,"IDVEND":1,"ID_BINARIO":null,"ID_N":"*********","ID_RES":0,"IMPCONSUMO":0,"IMPRESO":"N","INTERES_IMPLICITO":"N","LETRAS":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE","OBSERV":"Factura SEA3464 - GRUPO SAI SAS","PONUMBER":null,"PROYECTO":null,"REVISADO":"N","REVISOR":null,"S":1,"SALDO_DEUDA":null,"SALDO_DEUDA_ABONO":null,"SALESTAX":1527281.46,"SHIPTO":0,"SIN_CRUCE":"N","SUBTOTAL":8038323.45,"TIPO":"FIA","TOTAL":9565604.91,"TOTAL_REAL":9565604.91,"USERNAME":"SYSTEM","level":"debug","message":"Datos mapeados para CARPROEN:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.198Z"}
{"level":"debug","message":"Datos mapeados para CARPRODE (3 registros)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.198Z"}
{"level":"debug","message":"Insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:36:48.200Z","values":[1,1,"FIA",18134,"*********","2025-05-13T00:00:00.000Z",9565604.91,"SYSTEM","20/6/2025, 8:36:48 a","Factura SEA3464 - GRUPO SAI SAS","","","2025-05-13T00:00:00.000Z","NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE",1,0,"N","N","N",null,null,"N",null,"false",null,null,null,null,null,"N","","N","N",null,"N",null,8038323.45,1527281.46,0,9565604.91,null,null,"N",null,null,null,0,null,null]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:36:48.202Z","values":["FIA",18134,"*********",51950501,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de la comisión sobre ventas CS ",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-8038323.45,0,0,0,0,8038323.45,8038323.45,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:36:48.273Z","values":["FIA",18134,"*********",24080219,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de IVA descontable por comisión",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-1527281.46,0,0,0,0,8038323.45,1527281.46,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:36:48.275Z","values":["FIA",18134,"*********",22050101,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de cuenta por pagar a GRUPO SAI",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,9565604.91,9565604.91,0,0,0,8038323.45,0,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,null,null,"N","N",null,0]}
{"level":"info","message":"Consecutivo actualizado a 18134 para tipo FIA","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.283Z"}
{"level":"info","message":"Factura SEA3464 procesada exitosamente con batch 18134","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:36:48.283Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:38:05.010Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:38:05.011Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.255Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.258Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.295Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.301Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.302Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.334Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.334Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:10.335Z"}
{"estado":"APROBADO","id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","service_response":null,"timestamp":"2025-06-20T13:55:40.672Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:40.673Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.257Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.257Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.257Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.257Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.258Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.261Z"}
{"level":"info","message":"NIT principal corregido: ********* -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.262Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.263Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.267Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.268Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.268Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.271Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.272Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.273Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.276Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.277Z"}
{"BANCO":"","BATCH":18135,"CHEQUE":"","CHEQUE_POSTF":"false","COD_OPERACION":null,"CONCEPTO_NOTAFE":null,"CUDS":null,"CUFE":null,"DETALLE":"","DETERIORO_ESFA":"N","DOC_FISICO":null,"DUEDATE":"2025-05-13T00:00:00.000Z","E":1,"ENTREGADO":"N","ENVIADO":"N","EXPORTADA":"N","FECHA":"2025-05-13T00:00:00.000Z","FECHA_CHEQUE":null,"FECHA_CONTAB_CONSIG":"N","FECHA_FIN_PERIODO":null,"FECHA_HORA":"20/6/2025, 8:55:41 a","FECHA_INICIO_PERIODO":null,"FECHA_RESPUESTA_DIAN":null,"FECHA_REVISION":null,"FORMAPAGO":null,"IDVEND":1,"ID_BINARIO":null,"ID_N":"*********","ID_RES":0,"IMPCONSUMO":0,"IMPRESO":"N","INTERES_IMPLICITO":"N","LETRAS":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE","OBSERV":"Factura SEA3464 - GRUPO SAI SAS","PONUMBER":null,"PROYECTO":null,"REVISADO":"N","REVISOR":null,"S":1,"SALDO_DEUDA":null,"SALDO_DEUDA_ABONO":null,"SALESTAX":1527281.46,"SHIPTO":0,"SIN_CRUCE":"N","SUBTOTAL":8038323.45,"TIPO":"FIA","TOTAL":9565604.91,"TOTAL_REAL":9565604.91,"USERNAME":"SYSTEM","level":"debug","message":"Datos mapeados para CARPROEN:","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.314Z"}
{"level":"debug","message":"Datos mapeados para CARPRODE (3 registros)","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.315Z"}
{"level":"debug","message":"Insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:55:41.317Z","values":[1,1,"FIA",18135,"*********","2025-05-13T00:00:00.000Z",9565604.91,"SYSTEM","20/6/2025, 8:55:41 a","Factura SEA3464 - GRUPO SAI SAS","","","2025-05-13T00:00:00.000Z","NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE",1,0,"N","N","N",null,null,"N",null,"false",null,null,null,null,null,"N","","N","N",null,"N",null,8038323.45,1527281.46,0,9565604.91,null,null,"N",null,null,null,0,null,null]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:55:41.319Z","values":["FIA",18135,"*********",51950501,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de la comisión sobre ventas CS ",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-8038323.45,0,0,0,0,8038323.45,8038323.45,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:55:41.322Z","values":["FIA",18135,"*********",24080219,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de IVA descontable por comisión",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-1527281.46,0,0,0,0,8038323.45,1527281.46,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:55:41.323Z","values":["FIA",18135,"*********",22050101,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de cuenta por pagar a GRUPO SAI",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,9565604.91,9565604.91,0,0,0,8038323.45,0,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,null,null,"N","N",null,0]}
{"level":"info","message":"Consecutivo actualizado a 18135 para tipo FIA","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.332Z"}
{"estado":"SINCRONIZADO","level":"info","message":"Estado de factura 85 actualizado:","service":"supabase-firebird-sync","serviceResponse":"Ok","timestamp":"2025-06-20T13:55:41.529Z"}
{"level":"info","message":"Factura SEA3464 procesada exitosamente con batch 18135","service":"supabase-firebird-sync","timestamp":"2025-06-20T13:55:41.529Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:27.309Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:27.311Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:36.990Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:36.993Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:37.036Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:37.041Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:37.041Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:37.074Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:37.075Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:16:37.075Z"}
{"estado":"APROBADO","id":90,"invoice_number":"Z1982504020","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","service_response":null,"timestamp":"2025-06-20T14:17:41.505Z"}
{"level":"info","message":"Procesando factura aprobada: Z1982504020 (ID: 90)","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:41.506Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********1","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.049Z"}
{"level":"debug","message":"Variación 1 (original): *********1","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.049Z"}
{"level":"debug","message":"Variación 3 (sin último dígito): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.049Z"}
{"level":"debug","message":"Variaciones finales: *********1, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.050Z"}
{"0":"*********1","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.050Z"}
{"level":"info","message":"Tercero encontrado: *********1 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.075Z"}
{"level":"info","message":"NIT principal corregido: ********* -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.075Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********1","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.075Z"}
{"level":"debug","message":"Variación 1 (original): *********1","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.075Z"}
{"level":"debug","message":"Variación 3 (sin último dígito): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.076Z"}
{"level":"debug","message":"Variaciones finales: *********1, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.076Z"}
{"0":"*********1","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.076Z"}
{"level":"info","message":"Tercero encontrado: *********1 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.080Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********1 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.080Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********1","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.080Z"}
{"level":"debug","message":"Variación 1 (original): *********1","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.080Z"}
{"level":"debug","message":"Variación 3 (sin último dígito): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.080Z"}
{"level":"debug","message":"Variaciones finales: *********1, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.081Z"}
{"0":"*********1","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.081Z"}
{"level":"info","message":"Tercero encontrado: *********1 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.083Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********1 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.084Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********1","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.084Z"}
{"level":"debug","message":"Variación 1 (original): *********1","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.084Z"}
{"level":"debug","message":"Variación 3 (sin último dígito): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.084Z"}
{"level":"debug","message":"Variaciones finales: *********1, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.084Z"}
{"0":"*********1","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.084Z"}
{"level":"info","message":"Tercero encontrado: *********1 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.088Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********1 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.089Z"}
{"BANCO":"","BATCH":18136,"CHEQUE":"","CHEQUE_POSTF":"false","COD_OPERACION":null,"CONCEPTO_NOTAFE":null,"CUDS":null,"CUFE":null,"DETALLE":"","DETERIORO_ESFA":"N","DOC_FISICO":null,"DUEDATE":"2025-05-04T00:00:00.000Z","E":1,"ENTREGADO":"N","ENVIADO":"N","EXPORTADA":"N","FECHA":"2025-05-04T00:00:00.000Z","FECHA_CHEQUE":null,"FECHA_CONTAB_CONSIG":"N","FECHA_FIN_PERIODO":null,"FECHA_HORA":"20/6/2025, 9:17:42 a","FECHA_INICIO_PERIODO":null,"FECHA_RESPUESTA_DIAN":null,"FECHA_REVISION":null,"FORMAPAGO":null,"IDVEND":1,"ID_BINARIO":null,"ID_N":"*********","ID_RES":0,"IMPCONSUMO":0,"IMPRESO":"N","INTERES_IMPLICITO":"N","LETRAS":"TRES MILLONES SEISCIENTOS SETENTA Y UNO MIL NOVECIENTOS PESOS M/CTE","OBSERV":"Factura Z1982504020 - Alkosto S.A. Colombiana de Comercio S.A.","PONUMBER":null,"PROYECTO":null,"REVISADO":"N","REVISOR":null,"S":1,"SALDO_DEUDA":null,"SALDO_DEUDA_ABONO":null,"SALESTAX":586270,"SHIPTO":0,"SIN_CRUCE":"N","SUBTOTAL":3085630,"TIPO":"FIA","TOTAL":3671900,"TOTAL_REAL":3671900,"USERNAME":"SYSTEM","level":"debug","message":"Datos mapeados para CARPROEN:","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.124Z"}
{"level":"debug","message":"Datos mapeados para CARPRODE (3 registros)","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.125Z"}
{"level":"debug","message":"Insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T14:17:42.127Z","values":[1,1,"FIA",18136,"*********","2025-05-04T00:00:00.000Z",3671900,"SYSTEM","20/6/2025, 9:17:42 a","Factura Z1982504020 - Alkosto S.A. Colombiana de Comercio S.A.","","","2025-05-04T00:00:00.000Z","TRES MILLONES SEISCIENTOS SETENTA Y UNO MIL NOVECIENTOS PESOS M/CTE",1,0,"N","N","N",null,null,"N",null,"false",null,null,null,null,null,"N","","N","N",null,"N",null,3085630,586270,0,3671900,null,null,"N",null,null,null,0,null,null]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T14:17:42.167Z","values":["FIA",18136,"*********",14359501,1,1,"","Z1982504020","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Compra de Nevera LG SBS 519Lt GS51BPP'G ",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-3085630,0,0,0,0,3085630,3085630,1,null,"2025-05-04T00:00:00.000Z",null,null,"N",0,null,null,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T14:17:42.177Z","values":["FIA",18136,"*********",24080216,1,1,"","Z1982504020","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","IVA descontable de la compra según factu",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-586270,0,0,0,0,3085630,586270,1,null,"2025-05-04T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T14:17:42.179Z","values":["FIA",18136,"*********",22050101,1,1,"","Z1982504020","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Obligación con Alkosto S.A. por compra d",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,3671900,3671900,0,0,0,3671900,0,1,null,"2025-05-04T00:00:00.000Z",null,null,"N",0,null,null,null,"N","N",null,0]}
{"level":"info","message":"Consecutivo actualizado a 18136 para tipo FIA","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.190Z"}
{"estado":"SINCRONIZADO","level":"info","message":"Estado de factura 90 actualizado:","service":"supabase-firebird-sync","serviceResponse":"Ok","timestamp":"2025-06-20T14:17:42.354Z"}
{"level":"info","message":"Factura Z1982504020 procesada exitosamente con batch 18136","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:17:42.355Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:34.752Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:34.755Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:34.793Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:34.799Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:34.799Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:34.832Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:34.833Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:34.833Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:39.015Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:24:39.017Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:28:20.230Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:28:20.233Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:28:20.274Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:28:20.279Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:28:20.280Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:28:20.314Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:28:20.314Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:28:20.314Z"}
{"estado":"APROBADO","fecha_hora_sync":null,"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","service_response":"Ok","timestamp":"2025-06-20T14:28:28.785Z"}
{"level":"info","message":"Factura SEA3464 ya fue sincronizada, omitiendo procesamiento","service":"supabase-firebird-sync","timestamp":"2025-06-20T14:28:28.785Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:21:11.426Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:21:11.429Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:21:11.469Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:21:11.478Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:21:11.478Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:21:11.511Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:21:11.511Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:21:11.512Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:31.315Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:31.317Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:34.205Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:34.208Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:34.254Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:34.259Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:34.260Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:34.296Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:34.296Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:34.296Z"}
{"estado":"APROBADO","fecha_hora_sync":null,"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","service_response":"Ok","timestamp":"2025-06-20T15:22:50.177Z"}
{"level":"info","message":"Factura SEA3464 ya fue sincronizada, omitiendo procesamiento","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:22:50.178Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:26:05.402Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:26:05.404Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:30.391Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:30.394Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:30.433Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:30.439Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:30.440Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:30.474Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:30.475Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:30.475Z"}
{"estado":"APROBADO","fecha_hora_sync":null,"id":85,"invoice_number":"SEA3464","level":"info","message":"Factura aprobada detectada:","service":"supabase-firebird-sync","service_response":null,"timestamp":"2025-06-20T15:30:36.856Z"}
{"level":"info","message":"Procesando factura aprobada: SEA3464 (ID: 85)","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:36.857Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.496Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.497Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.497Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.497Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.497Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.503Z"}
{"level":"info","message":"NIT principal corregido: ********* -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.503Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.503Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.504Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.504Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.504Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.504Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.507Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.507Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.507Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.507Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.507Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.508Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.508Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.511Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.512Z"}
{"level":"debug","message":"Generando variaciones para NIT: *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.512Z"}
{"level":"debug","message":"Variación 1 (original): *********-4","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.512Z"}
{"level":"debug","message":"Variación 2 (sin DV después de guión): *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.512Z"}
{"level":"debug","message":"Variaciones finales: *********-4, *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.512Z"}
{"0":"*********-4","1":"*********","level":"debug","message":"Buscando tercero con variaciones:","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.512Z"}
{"level":"info","message":"Tercero encontrado: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.515Z"}
{"level":"info","message":"NIT de entrada contable corregido: *********-4 -> *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.515Z"}
{"BANCO":"","BATCH":18134,"CHEQUE":"","CHEQUE_POSTF":"false","COD_OPERACION":null,"CONCEPTO_NOTAFE":null,"CUDS":null,"CUFE":null,"DETALLE":"","DETERIORO_ESFA":"N","DOC_FISICO":null,"DUEDATE":"2025-05-13T00:00:00.000Z","E":1,"ENTREGADO":"N","ENVIADO":"N","EXPORTADA":"N","FECHA":"2025-05-13T00:00:00.000Z","FECHA_CHEQUE":null,"FECHA_CONTAB_CONSIG":"N","FECHA_FIN_PERIODO":null,"FECHA_HORA":"20/6/2025, 10:30:37 ","FECHA_INICIO_PERIODO":null,"FECHA_RESPUESTA_DIAN":null,"FECHA_REVISION":null,"FORMAPAGO":null,"IDVEND":1,"ID_BINARIO":null,"ID_N":"*********","ID_RES":0,"IMPCONSUMO":0,"IMPRESO":"N","INTERES_IMPLICITO":"N","LETRAS":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE","OBSERV":"Factura SEA3464 - GRUPO SAI SAS","PONUMBER":null,"PROYECTO":null,"REVISADO":"N","REVISOR":null,"S":1,"SALDO_DEUDA":null,"SALDO_DEUDA_ABONO":null,"SALESTAX":1527281.46,"SHIPTO":0,"SIN_CRUCE":"N","SUBTOTAL":8038323.45,"TIPO":"FIA","TOTAL":9565604.91,"TOTAL_REAL":9565604.91,"USERNAME":"SYSTEM","level":"debug","message":"Datos mapeados para CARPROEN:","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.775Z"}
{"level":"debug","message":"Datos mapeados para CARPRODE (3 registros)","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.776Z"}
{"level":"debug","message":"Insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T15:30:37.777Z","values":[1,1,"FIA",18134,"*********","2025-05-13T00:00:00.000Z",9565604.91,"SYSTEM","20/6/2025, 10:30:37 ","Factura SEA3464 - GRUPO SAI SAS","","","2025-05-13T00:00:00.000Z","NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE",1,0,"N","N","N",null,null,"N",null,"false",null,null,null,null,null,"N","","N","N",null,"N",null,8038323.45,1527281.46,0,9565604.91,null,null,"N",null,null,null,0,null,null]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T15:30:37.779Z","values":["FIA",18134,"*********",51950501,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de la comisión sobre ventas CS ",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-8038323.45,0,0,0,0,8038323.45,8038323.45,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T15:30:37.781Z","values":["FIA",18134,"*********",24080219,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de IVA descontable por comisión",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,-1527281.46,0,0,0,0,8038323.45,1527281.46,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,19,null,"N","N",null,0]}
{"level":"debug","message":"Insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T15:30:37.782Z","values":["FIA",18134,"*********",22050101,1,1,"","SEA3464","2025-06-06T00:00:00.000Z","2025-06-06T00:00:00.000Z",0,0,"","Registro de cuenta por pagar a GRUPO SAI",0,1,null,null,"",0,0,null,null,"",null,null,0,"false",null,9565604.91,9565604.91,0,0,0,8038323.45,0,1,null,"2025-05-13T00:00:00.000Z",null,null,"N",0,null,null,null,"N","N",null,0]}
{"level":"info","message":"Consecutivo actualizado a 18134 para tipo FIA","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.788Z"}
{"estado":"SINCRONIZADO","level":"info","message":"Estado de factura 85 actualizado:","service":"supabase-firebird-sync","serviceResponse":"Ok","timestamp":"2025-06-20T15:30:37.967Z"}
{"level":"info","message":"Factura SEA3464 procesada exitosamente con batch 18134","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:30:37.967Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:36:03.868Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T15:36:03.869Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.669Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.672Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.673Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.709Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.715Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.715Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.726Z"}
{"level":"info","message":"Servicio de sincronización de terceros inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.727Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.760Z"}
{"level":"info","message":"Sincronizaciones en segundo plano configuradas","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.761Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.761Z"}
{"level":"info","message":"Sincronización de terceros programada cada 30 minutos","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.761Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.765Z"}
{"level":"info","message":"API de control disponible en http://localhost:3001","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.766Z"}
{"level":"info","message":"Endpoints disponibles:","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.766Z"}
{"level":"info","message":"  GET  /health - Estado del servicio","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.766Z"}
{"level":"info","message":"  GET  /api/sync/status - Estado de sincronización","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.766Z"}
{"level":"info","message":"  POST /api/sync/third-parties - Sincronización manual de terceros","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.766Z"}
{"level":"info","message":"  GET  /api/sync/third-parties/stats - Estadísticas de terceros","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:31:38.766Z"}
{"level":"info","message":"Ejecutando sincronización inicial de terceros...","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:38.762Z"}
{"level":"info","message":"Iniciando sincronización automática de terceros...","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:38.763Z"}
{"level":"info","message":"Iniciando sincronización incremental de terceros...","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:38.763Z"}
{"level":"info","message":"Iniciando sincronización de terceros (fullSync: false)","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:38.763Z"}
{"level":"info","message":"Última versión sincronizada: 0","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:39.302Z"}
{"level":"info","message":"Encontrados 623 terceros para sincronizar","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:39.371Z"}
{"level":"info","message":"Procesando lote 1 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:39.371Z"}
{"level":"debug","message":"Procesando tercero: 800231967","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:39.372Z"}
{"level":"debug","message":"Tercero insertado: 800231967","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:40.320Z"}
{"level":"debug","message":"Procesando tercero: 1","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:40.320Z"}
{"level":"debug","message":"Tercero insertado: 1","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:40.898Z"}
{"level":"debug","message":"Procesando tercero: 800229739","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:40.898Z"}
{"level":"debug","message":"Tercero insertado: 800229739","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:41.263Z"}
{"level":"debug","message":"Procesando tercero: 999999","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:41.263Z"}
{"level":"debug","message":"Tercero insertado: 999999","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:41.837Z"}
{"level":"debug","message":"Procesando tercero: 800224827","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:41.837Z"}
{"level":"debug","message":"Tercero insertado: 800224827","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:42.173Z"}
{"level":"debug","message":"Procesando tercero: 805001157","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:42.173Z"}
{"level":"debug","message":"Tercero insertado: 805001157","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:42.527Z"}
{"level":"debug","message":"Procesando tercero: 800088702","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:42.527Z"}
{"level":"debug","message":"Tercero insertado: 800088702","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:42.871Z"}
{"level":"debug","message":"Procesando tercero: 805000427","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:42.871Z"}
{"level":"debug","message":"Tercero insertado: 805000427","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:43.198Z"}
{"level":"debug","message":"Procesando tercero: 800144331","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:43.198Z"}
{"level":"debug","message":"Tercero insertado: 800144331","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:43.526Z"}
{"level":"debug","message":"Procesando tercero: 800140949","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:43.527Z"}
{"level":"debug","message":"Tercero insertado: 800140949","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:43.843Z"}
{"level":"info","message":"Procesando lote 2 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:43.955Z"}
{"level":"debug","message":"Procesando tercero: 860007336","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:43.956Z"}
{"level":"debug","message":"Tercero insertado: 860007336","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:44.324Z"}
{"level":"debug","message":"Procesando tercero: 830009783","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:44.324Z"}
{"level":"debug","message":"Tercero insertado: 830009783","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:44.680Z"}
{"level":"debug","message":"Procesando tercero: 830113831","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:44.681Z"}
{"level":"debug","message":"Tercero insertado: 830113831","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:45.011Z"}
{"level":"debug","message":"Procesando tercero: 800256161","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:45.011Z"}
{"level":"debug","message":"Tercero insertado: 800256161","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:45.348Z"}
{"level":"debug","message":"Procesando tercero: 830003564","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:45.348Z"}
{"level":"debug","message":"Tercero insertado: 830003564","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:45.673Z"}
{"level":"debug","message":"Procesando tercero: 899999239","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:45.674Z"}
{"level":"debug","message":"Tercero insertado: 899999239","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:46.013Z"}
{"level":"debug","message":"Procesando tercero: 800227940","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:46.013Z"}
{"level":"debug","message":"Tercero insertado: 800227940","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:46.333Z"}
{"level":"debug","message":"Procesando tercero: 890303208","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:46.334Z"}
{"level":"debug","message":"Tercero insertado: 890303208","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:46.686Z"}
{"level":"debug","message":"Procesando tercero: 890303093","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:46.686Z"}
{"level":"debug","message":"Tercero insertado: 890303093","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:47.007Z"}
{"level":"debug","message":"Procesando tercero: 800226175","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:47.007Z"}
{"level":"debug","message":"Tercero insertado: 800226175","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:47.334Z"}
{"level":"info","message":"Procesando lote 3 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:47.444Z"}
{"level":"debug","message":"Procesando tercero: 800130907","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:47.445Z"}
{"level":"debug","message":"Tercero insertado: 800130907","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:47.805Z"}
{"level":"debug","message":"Procesando tercero: 899999034","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:47.805Z"}
{"level":"debug","message":"Tercero insertado: 899999034","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:48.160Z"}
{"level":"debug","message":"Procesando tercero: 22222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:48.160Z"}
{"level":"debug","message":"Tercero insertado: 22222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:48.511Z"}
{"level":"debug","message":"Procesando tercero: 0001","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:48.511Z"}
{"level":"debug","message":"Tercero insertado: 0001","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:48.866Z"}
{"level":"debug","message":"Procesando tercero: 0003","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:48.866Z"}
{"level":"debug","message":"Tercero insertado: 0003","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:49.212Z"}
{"level":"debug","message":"Procesando tercero: 0004","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:49.213Z"}
{"level":"debug","message":"Tercero insertado: 0004","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:49.560Z"}
{"level":"debug","message":"Procesando tercero: 0005","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:49.560Z"}
{"level":"debug","message":"Tercero insertado: 0005","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:49.916Z"}
{"level":"debug","message":"Procesando tercero: 0006","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:49.917Z"}
{"level":"debug","message":"Tercero insertado: 0006","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:50.274Z"}
{"level":"debug","message":"Procesando tercero: 0007","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:50.274Z"}
{"level":"debug","message":"Tercero insertado: 0007","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:50.627Z"}
{"level":"debug","message":"Procesando tercero: 0008","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:50.627Z"}
{"level":"debug","message":"Tercero insertado: 0008","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:50.970Z"}
{"level":"info","message":"Procesando lote 4 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:51.071Z"}
{"level":"debug","message":"Procesando tercero: 0009","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:51.072Z"}
{"level":"debug","message":"Tercero insertado: 0009","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:51.408Z"}
{"level":"debug","message":"Procesando tercero: 0010","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:51.409Z"}
{"level":"debug","message":"Tercero insertado: 0010","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:51.736Z"}
{"level":"debug","message":"Procesando tercero: 0011","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:51.736Z"}
{"level":"debug","message":"Tercero insertado: 0011","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:52.119Z"}
{"level":"debug","message":"Procesando tercero: 0012","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:52.120Z"}
{"level":"debug","message":"Tercero insertado: 0012","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:52.575Z"}
{"level":"debug","message":"Procesando tercero: 1005876443","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:52.575Z"}
{"level":"debug","message":"Tercero insertado: 1005876443","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:52.953Z"}
{"level":"debug","message":"Procesando tercero: 10062793","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:52.953Z"}
{"level":"debug","message":"Tercero insertado: 10062793","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:53.333Z"}
{"level":"debug","message":"Procesando tercero: 1006359362","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:53.333Z"}
{"level":"debug","message":"Tercero insertado: 1006359362","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:53.677Z"}
{"level":"debug","message":"Procesando tercero: 1006359858","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:53.677Z"}
{"level":"debug","message":"Tercero insertado: 1006359858","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:54.046Z"}
{"level":"debug","message":"Procesando tercero: 10081993","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:54.047Z"}
{"level":"debug","message":"Tercero insertado: 10081993","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:54.385Z"}
{"level":"debug","message":"Procesando tercero: 10138701","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:54.385Z"}
{"level":"debug","message":"Tercero insertado: 10138701","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:54.717Z"}
{"level":"info","message":"Procesando lote 5 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:54.819Z"}
{"level":"debug","message":"Procesando tercero: 108690155","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:54.819Z"}
{"level":"debug","message":"Tercero insertado: 108690155","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:55.147Z"}
{"level":"debug","message":"Procesando tercero: 11070364767","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:55.147Z"}
{"level":"debug","message":"Tercero insertado: 11070364767","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:55.516Z"}
{"level":"debug","message":"Procesando tercero: 1107066807","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:55.516Z"}
{"level":"debug","message":"Tercero insertado: 1107066807","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:55.838Z"}
{"level":"debug","message":"Procesando tercero: 1107085383","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:55.838Z"}
{"level":"debug","message":"Tercero insertado: 1107085383","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:56.170Z"}
{"level":"debug","message":"Procesando tercero: 1113516463","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:56.171Z"}
{"level":"debug","message":"Tercero insertado: 1113516463","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:56.513Z"}
{"level":"debug","message":"Procesando tercero: 1113784449","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:56.514Z"}
{"level":"debug","message":"Tercero insertado: 1113784449","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:56.850Z"}
{"level":"debug","message":"Procesando tercero: 1114393262","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:56.850Z"}
{"level":"debug","message":"Tercero insertado: 1114393262","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:57.177Z"}
{"level":"debug","message":"Procesando tercero: 111472618","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:57.177Z"}
{"level":"debug","message":"Tercero insertado: 111472618","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:57.508Z"}
{"level":"debug","message":"Procesando tercero: 1114726674","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:57.508Z"}
{"level":"debug","message":"Tercero insertado: 1114726674","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:57.837Z"}
{"level":"debug","message":"Procesando tercero: 1114726998","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:57.837Z"}
{"level":"debug","message":"Tercero insertado: 1114726998","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:58.151Z"}
{"level":"info","message":"Procesando lote 6 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:58.266Z"}
{"level":"debug","message":"Procesando tercero: 1114727419","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:58.266Z"}
{"level":"debug","message":"Tercero insertado: 1114727419","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:58.596Z"}
{"level":"debug","message":"Procesando tercero: 1114727810","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:58.596Z"}
{"level":"debug","message":"Tercero insertado: 1114727810","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:58.912Z"}
{"level":"debug","message":"Procesando tercero: 1114727934","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:58.912Z"}
{"level":"debug","message":"Tercero insertado: 1114727934","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:59.261Z"}
{"level":"debug","message":"Procesando tercero: 1114728447","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:59.261Z"}
{"level":"debug","message":"Tercero insertado: 1114728447","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:59.586Z"}
{"level":"debug","message":"Procesando tercero: 1114728861","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:59.586Z"}
{"level":"debug","message":"Tercero insertado: 1114728861","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:59.945Z"}
{"level":"debug","message":"Procesando tercero: 1114729795","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:32:59.945Z"}
{"level":"debug","message":"Tercero insertado: 1114729795","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:00.261Z"}
{"level":"debug","message":"Procesando tercero: 1114731420","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:00.262Z"}
{"level":"debug","message":"Tercero insertado: 1114731420","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:00.598Z"}
{"level":"debug","message":"Procesando tercero: 1114731799","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:00.598Z"}
{"level":"debug","message":"Tercero insertado: 1114731799","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:00.944Z"}
{"level":"debug","message":"Procesando tercero: 1114732248","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:00.944Z"}
{"level":"debug","message":"Tercero insertado: 1114732248","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:01.269Z"}
{"level":"debug","message":"Procesando tercero: 1114732667","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:01.270Z"}
{"level":"debug","message":"Tercero insertado: 1114732667","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:01.615Z"}
{"level":"info","message":"Procesando lote 7 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:01.717Z"}
{"level":"debug","message":"Procesando tercero: 1114733038","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:01.718Z"}
{"level":"debug","message":"Tercero insertado: 1114733038","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:02.301Z"}
{"level":"debug","message":"Procesando tercero: 1114733366","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:02.301Z"}
{"level":"debug","message":"Tercero insertado: 1114733366","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:02.658Z"}
{"level":"debug","message":"Procesando tercero: 1114733596","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:02.659Z"}
{"level":"debug","message":"Tercero insertado: 1114733596","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:03.017Z"}
{"level":"debug","message":"Procesando tercero: 1114733932","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:03.018Z"}
{"level":"debug","message":"Tercero insertado: 1114733932","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:03.408Z"}
{"level":"debug","message":"Procesando tercero: 1114734242","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:03.409Z"}
{"level":"debug","message":"Tercero insertado: 1114734242","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:03.740Z"}
{"level":"debug","message":"Procesando tercero: 1114734718","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:03.741Z"}
{"level":"debug","message":"Tercero insertado: 1114734718","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:04.305Z"}
{"level":"debug","message":"Procesando tercero: 1114735153","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:04.305Z"}
{"level":"debug","message":"Tercero insertado: 1114735153","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:04.634Z"}
{"level":"debug","message":"Procesando tercero: 1114735936","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:04.634Z"}
{"level":"debug","message":"Tercero insertado: 1114735936","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:04.968Z"}
{"level":"debug","message":"Procesando tercero: 1114736189","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:04.968Z"}
{"level":"debug","message":"Tercero insertado: 1114736189","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:05.294Z"}
{"level":"debug","message":"Procesando tercero: 1114736709","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:05.295Z"}
{"level":"debug","message":"Tercero insertado: 1114736709","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:05.623Z"}
{"level":"info","message":"Procesando lote 8 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:05.732Z"}
{"level":"debug","message":"Procesando tercero: 1114750578","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:05.733Z"}
{"level":"debug","message":"Tercero insertado: 1114750578","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:06.078Z"}
{"level":"debug","message":"Procesando tercero: 1114812865","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:06.078Z"}
{"level":"debug","message":"Tercero insertado: 1114812865","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:06.409Z"}
{"level":"debug","message":"Procesando tercero: 1115072887","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:06.409Z"}
{"level":"debug","message":"Tercero insertado: 1115072887","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:06.746Z"}
{"level":"debug","message":"Procesando tercero: 1116235807","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:06.746Z"}
{"level":"debug","message":"Tercero insertado: 1116235807","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:07.092Z"}
{"level":"debug","message":"Procesando tercero: 1118285735","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:07.092Z"}
{"level":"debug","message":"Tercero insertado: 1118285735","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:07.472Z"}
{"level":"debug","message":"Procesando tercero: 1130630754","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:07.472Z"}
{"level":"debug","message":"Tercero insertado: 1130630754","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:07.794Z"}
{"level":"debug","message":"Procesando tercero: 1130659497","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:07.795Z"}
{"level":"debug","message":"Tercero insertado: 1130659497","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:08.156Z"}
{"level":"debug","message":"Procesando tercero: 1130677925","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:08.156Z"}
{"level":"debug","message":"Tercero insertado: 1130677925","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:08.484Z"}
{"level":"debug","message":"Procesando tercero: 11314687","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:08.484Z"}
{"level":"debug","message":"Tercero insertado: 11314687","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:08.820Z"}
{"level":"debug","message":"Procesando tercero: 1143931058","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:08.820Z"}
{"level":"debug","message":"Tercero insertado: 1143931058","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:09.152Z"}
{"level":"info","message":"Procesando lote 9 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:09.256Z"}
{"level":"debug","message":"Procesando tercero: 1143958319","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:09.257Z"}
{"level":"debug","message":"Tercero insertado: 1143958319","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:09.602Z"}
{"level":"debug","message":"Procesando tercero: 1143976708","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:09.602Z"}
{"level":"debug","message":"Tercero insertado: 1143976708","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:09.967Z"}
{"level":"debug","message":"Procesando tercero: 1144034906","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:09.968Z"}
{"level":"debug","message":"Tercero insertado: 1144034906","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:10.307Z"}
{"level":"debug","message":"Procesando tercero: 1144065430","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:10.308Z"}
{"level":"debug","message":"Tercero insertado: 1144065430","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:10.654Z"}
{"level":"debug","message":"Procesando tercero: 1144084570","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:10.654Z"}
{"level":"debug","message":"Tercero insertado: 1144084570","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:11.010Z"}
{"level":"debug","message":"Procesando tercero: 1144146688","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:11.010Z"}
{"level":"debug","message":"Tercero insertado: 1144146688","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:11.352Z"}
{"level":"debug","message":"Procesando tercero: 114727810","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:11.352Z"}
{"level":"debug","message":"Tercero insertado: 114727810","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:11.867Z"}
{"level":"debug","message":"Procesando tercero: 1151950140","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:11.867Z"}
{"level":"debug","message":"Tercero insertado: 1151950140","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:12.210Z"}
{"level":"debug","message":"Procesando tercero: 1193105130","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:12.211Z"}
{"level":"debug","message":"Tercero insertado: 1193105130","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:12.556Z"}
{"level":"debug","message":"Procesando tercero: 1193206354","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:12.556Z"}
{"level":"debug","message":"Tercero insertado: 1193206354","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:12.896Z"}
{"level":"info","message":"Procesando lote 10 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:13.009Z"}
{"level":"debug","message":"Procesando tercero: 1193518126","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:13.009Z"}
{"level":"debug","message":"Tercero insertado: 1193518126","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:13.367Z"}
{"level":"debug","message":"Procesando tercero: 1193546868","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:13.367Z"}
{"level":"debug","message":"Tercero insertado: 1193546868","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:13.715Z"}
{"level":"debug","message":"Procesando tercero: 12200764","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:13.715Z"}
{"level":"debug","message":"Tercero insertado: 12200764","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:14.047Z"}
{"level":"debug","message":"Procesando tercero: 12754202","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:14.047Z"}
{"level":"debug","message":"Tercero insertado: 12754202","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:14.374Z"}
{"level":"debug","message":"Procesando tercero: 12977507","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:14.375Z"}
{"level":"debug","message":"Tercero insertado: 12977507","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:14.722Z"}
{"level":"debug","message":"Procesando tercero: 14465437","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:14.722Z"}
{"level":"debug","message":"Tercero insertado: 14465437","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:15.056Z"}
{"level":"debug","message":"Procesando tercero: 14575603","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:15.056Z"}
{"level":"debug","message":"Tercero insertado: 14575603","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:15.376Z"}
{"level":"debug","message":"Procesando tercero: 14576058","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:15.376Z"}
{"level":"debug","message":"Tercero insertado: 14576058","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:15.695Z"}
{"level":"debug","message":"Procesando tercero: 14576134","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:15.696Z"}
{"level":"debug","message":"Tercero insertado: 14576134","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:16.031Z"}
{"level":"debug","message":"Procesando tercero: 14576142","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:16.032Z"}
{"level":"debug","message":"Tercero insertado: 14576142","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:16.380Z"}
{"level":"info","message":"Procesando lote 11 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:16.489Z"}
{"level":"debug","message":"Procesando tercero: 14578142","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:16.490Z"}
{"level":"debug","message":"Tercero insertado: 14578142","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:16.808Z"}
{"level":"debug","message":"Procesando tercero: 14621720","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:16.808Z"}
{"level":"debug","message":"Tercero insertado: 14621720","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:17.140Z"}
{"level":"debug","message":"Procesando tercero: 1466610","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:17.140Z"}
{"level":"debug","message":"Tercero insertado: 1466610","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:17.484Z"}
{"level":"debug","message":"Procesando tercero: 16285724","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:17.484Z"}
{"level":"debug","message":"Tercero insertado: 16285724","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:17.823Z"}
{"level":"debug","message":"Procesando tercero: 16287382","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:17.823Z"}
{"level":"debug","message":"Tercero insertado: 16287382","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:18.152Z"}
{"level":"debug","message":"Procesando tercero: 16340659","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:18.152Z"}
{"level":"debug","message":"Tercero insertado: 16340659","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:18.470Z"}
{"level":"debug","message":"Procesando tercero: 16366249","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:18.470Z"}
{"level":"debug","message":"Tercero insertado: 16366249","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:18.832Z"}
{"level":"debug","message":"Procesando tercero: 16376468","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:18.832Z"}
{"level":"debug","message":"Tercero insertado: 16376468","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:19.145Z"}
{"level":"debug","message":"Procesando tercero: 16457516","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:19.145Z"}
{"level":"debug","message":"Tercero insertado: 16457516","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:19.461Z"}
{"level":"debug","message":"Procesando tercero: 16551468","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:19.461Z"}
{"level":"debug","message":"Tercero insertado: 16551468","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:19.793Z"}
{"level":"info","message":"Procesando lote 12 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:19.895Z"}
{"level":"debug","message":"Procesando tercero: 16582636","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:19.895Z"}
{"level":"debug","message":"Tercero insertado: 16582636","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:20.226Z"}
{"level":"debug","message":"Procesando tercero: 16589725","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:20.226Z"}
{"level":"debug","message":"Tercero insertado: 16589725","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:20.599Z"}
{"level":"debug","message":"Procesando tercero: 16609394","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:20.599Z"}
{"level":"debug","message":"Tercero insertado: 16609394","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:20.931Z"}
{"level":"debug","message":"Procesando tercero: 16635161","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:20.931Z"}
{"level":"debug","message":"Tercero insertado: 16635161","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:21.281Z"}
{"level":"debug","message":"Procesando tercero: 16692871","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:21.281Z"}
{"level":"debug","message":"Tercero insertado: 16692871","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:21.631Z"}
{"level":"debug","message":"Procesando tercero: 16732670","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:21.631Z"}
{"level":"debug","message":"Tercero insertado: 16732670","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:22.151Z"}
{"level":"debug","message":"Procesando tercero: 16770996","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:22.151Z"}
{"level":"debug","message":"Tercero insertado: 16770996","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:22.551Z"}
{"level":"debug","message":"Procesando tercero: 16782895","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:22.552Z"}
{"level":"debug","message":"Tercero insertado: 16782895","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:22.919Z"}
{"level":"debug","message":"Procesando tercero: 16786732","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:22.919Z"}
{"level":"debug","message":"Tercero insertado: 16786732","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:23.250Z"}
{"level":"debug","message":"Procesando tercero: 16840446","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:23.250Z"}
{"level":"debug","message":"Tercero insertado: 16840446","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:23.576Z"}
{"level":"info","message":"Procesando lote 13 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:23.680Z"}
{"level":"debug","message":"Procesando tercero: 16894661","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:23.680Z"}
{"level":"debug","message":"Tercero insertado: 16894661","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:24.018Z"}
{"level":"debug","message":"Procesando tercero: 18378172","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:24.018Z"}
{"level":"debug","message":"Tercero insertado: 18378172","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:24.344Z"}
{"level":"debug","message":"Procesando tercero: 18386072","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:24.345Z"}
{"level":"debug","message":"Tercero insertado: 18386072","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:24.673Z"}
{"level":"debug","message":"Procesando tercero: 18410545","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:24.673Z"}
{"level":"debug","message":"Tercero insertado: 18410545","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:25.016Z"}
{"level":"debug","message":"Procesando tercero: 18508590","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:25.017Z"}
{"level":"debug","message":"Tercero insertado: 18508590","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:25.350Z"}
{"level":"debug","message":"Procesando tercero: 19434695","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:25.351Z"}
{"level":"debug","message":"Tercero insertado: 19434695","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:25.690Z"}
{"level":"debug","message":"Procesando tercero: 21776241","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:25.690Z"}
{"level":"debug","message":"Tercero insertado: 21776241","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:26.045Z"}
{"level":"debug","message":"Procesando tercero: 21779166","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:26.046Z"}
{"level":"debug","message":"Tercero insertado: 21779166","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:26.373Z"}
{"level":"debug","message":"Procesando tercero: 21863167","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:26.373Z"}
{"level":"debug","message":"Tercero insertado: 21863167","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:26.691Z"}
{"level":"debug","message":"Procesando tercero: 22166926","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:26.691Z"}
{"level":"debug","message":"Tercero insertado: 22166926","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:27.016Z"}
{"level":"info","message":"Procesando lote 14 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:27.125Z"}
{"level":"debug","message":"Procesando tercero: 24660603","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:27.126Z"}
{"level":"debug","message":"Tercero insertado: 24660603","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:27.461Z"}
{"level":"debug","message":"Procesando tercero: 25078884","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:27.461Z"}
{"level":"debug","message":"Tercero insertado: 25078884","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:27.795Z"}
{"level":"debug","message":"Procesando tercero: 2531272","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:27.795Z"}
{"level":"debug","message":"Tercero insertado: 2531272","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:28.121Z"}
{"level":"debug","message":"Procesando tercero: 2550042","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:28.121Z"}
{"level":"debug","message":"Tercero insertado: 2550042","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:28.446Z"}
{"level":"debug","message":"Procesando tercero: 2550267","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:28.446Z"}
{"level":"debug","message":"Tercero insertado: 2550267","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:28.759Z"}
{"level":"debug","message":"Procesando tercero: 2555593","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:28.759Z"}
{"level":"debug","message":"Tercero insertado: 2555593","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:29.074Z"}
{"level":"debug","message":"Procesando tercero: 27058036","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:29.074Z"}
{"level":"debug","message":"Tercero insertado: 27058036","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:29.395Z"}
{"level":"debug","message":"Procesando tercero: 29398379","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:29.396Z"}
{"level":"debug","message":"Tercero insertado: 29398379","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:29.715Z"}
{"level":"debug","message":"Procesando tercero: 29398872","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:29.715Z"}
{"level":"debug","message":"Tercero insertado: 29398872","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:30.033Z"}
{"level":"debug","message":"Procesando tercero: 29399244","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:30.033Z"}
{"level":"debug","message":"Tercero insertado: 29399244","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:30.349Z"}
{"level":"info","message":"Procesando lote 15 de 63","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:30.460Z"}
{"level":"debug","message":"Procesando tercero: 29399837","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:30.461Z"}
{"level":"debug","message":"Tercero insertado: 29399837","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:30.783Z"}
{"level":"debug","message":"Procesando tercero: 29400675","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:30.783Z"}
{"level":"debug","message":"Tercero insertado: 29400675","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:31.101Z"}
{"level":"debug","message":"Procesando tercero: 29401762","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:31.101Z"}
{"level":"debug","message":"Tercero insertado: 29401762","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:31.437Z"}
{"level":"debug","message":"Procesando tercero: 29757514","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:31.437Z"}
{"level":"debug","message":"Tercero insertado: 29757514","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:31.769Z"}
{"level":"debug","message":"Procesando tercero: 29775272","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:31.769Z"}
{"level":"debug","message":"Tercero insertado: 29775272","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:32.100Z"}
{"level":"debug","message":"Procesando tercero: 3106428675","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:32.100Z"}
{"level":"debug","message":"Tercero insertado: 3106428675","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:32.436Z"}
{"level":"debug","message":"Procesando tercero: 3117892424","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:32.437Z"}
{"level":"debug","message":"Tercero insertado: 3117892424","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:32.765Z"}
{"level":"debug","message":"Procesando tercero: 31286800","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:32.765Z"}
{"level":"debug","message":"Tercero insertado: 31286800","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:33.124Z"}
{"level":"debug","message":"Procesando tercero: 31482536","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:33.124Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:33.363Z"}
{"level":"info","message":"Intervalo de sincronización de terceros detenido","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:33.364Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:33.365Z"}
{"level":"info","message":"Cliente Supabase cerrado","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:33.365Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T16:33:33.365Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.148Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.152Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.152Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.153Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.189Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.195Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.195Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.206Z"}
{"level":"info","message":"Servicio de sincronización de terceros inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.207Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.219Z"}
{"level":"info","message":"Servicio de sincronización de cuentas contables inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.219Z"}
{"accountRanges":[{"end":********,"start":********},{"end":********,"start":********},{"end":********,"start":********},{"end":********,"start":********},{"end":********,"start":********}],"excludeZeroLevel":true,"level":"info","message":"Configuración de sincronización:","onlyActiveAccounts":true,"service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.220Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.253Z"}
{"level":"info","message":"Sincronizaciones en segundo plano configuradas","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.253Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.253Z"}
{"level":"info","message":"Sincronización de terceros programada cada 30 minutos","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.254Z"}
{"level":"info","message":"Sincronización de cuentas programada cada 120 minutos","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.254Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.257Z"}
{"level":"info","message":"API de control disponible en http://localhost:3001","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.258Z"}
{"level":"info","message":"Endpoints disponibles:","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.258Z"}
{"level":"info","message":"  GET  /health - Estado del servicio","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.259Z"}
{"level":"info","message":"  GET  /api/sync/status - Estado de sincronización","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.259Z"}
{"level":"info","message":"  POST /api/sync/third-parties - Sincronización manual de terceros","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.259Z"}
{"level":"info","message":"  GET  /api/sync/third-parties/stats - Estadísticas de terceros","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.259Z"}
{"level":"info","message":"  POST /api/sync/chart-of-accounts - Sincronización manual de cuentas","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.259Z"}
{"level":"info","message":"  GET  /api/sync/chart-of-accounts/stats - Estadísticas de cuentas","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.259Z"}
{"level":"info","message":"  GET  /api/sync/chart-of-accounts/config - Configuración de cuentas","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:25:50.259Z"}
{"level":"info","message":"Ejecutando sincronizaciones iniciales...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:50.266Z"}
{"level":"info","message":"Iniciando sincronización automática de terceros...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:50.266Z"}
{"level":"info","message":"Iniciando sincronización incremental de terceros...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:50.267Z"}
{"level":"info","message":"Iniciando sincronización de terceros (fullSync: false)","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:50.267Z"}
{"level":"info","message":"Última versión sincronizada: 128","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:50.940Z"}
{"level":"info","message":"Encontrados 497 terceros para sincronizar","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:50.989Z"}
{"level":"info","message":"Procesando lote 1 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:50.989Z"}
{"level":"debug","message":"Procesando tercero: 800231967","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:50.990Z"}
{"level":"debug","message":"Tercero actualizado: 800231967","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:51.661Z"}
{"level":"debug","message":"Procesando tercero: 1","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:51.661Z"}
{"level":"debug","message":"Tercero actualizado: 1","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:52.358Z"}
{"level":"debug","message":"Procesando tercero: 800229739","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:52.359Z"}
{"level":"debug","message":"Tercero actualizado: 800229739","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:52.994Z"}
{"level":"debug","message":"Procesando tercero: 999999","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:52.995Z"}
{"level":"debug","message":"Tercero actualizado: 999999","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:53.354Z"}
{"level":"debug","message":"Procesando tercero: 800224827","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:53.355Z"}
{"level":"debug","message":"Tercero actualizado: 800224827","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:53.710Z"}
{"level":"debug","message":"Procesando tercero: 805001157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:53.711Z"}
{"level":"debug","message":"Tercero actualizado: 805001157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:54.089Z"}
{"level":"debug","message":"Procesando tercero: 800088702","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:54.089Z"}
{"level":"debug","message":"Tercero actualizado: 800088702","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:54.444Z"}
{"level":"debug","message":"Procesando tercero: 805000427","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:54.444Z"}
{"level":"debug","message":"Tercero actualizado: 805000427","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:54.831Z"}
{"level":"debug","message":"Procesando tercero: 800144331","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:54.832Z"}
{"level":"debug","message":"Tercero actualizado: 800144331","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:55.188Z"}
{"level":"debug","message":"Procesando tercero: 800140949","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:55.189Z"}
{"level":"debug","message":"Tercero actualizado: 800140949","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:55.545Z"}
{"level":"info","message":"Procesando lote 2 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:55.650Z"}
{"level":"debug","message":"Procesando tercero: 860007336","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:55.651Z"}
{"level":"debug","message":"Tercero actualizado: 860007336","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:56.047Z"}
{"level":"debug","message":"Procesando tercero: 830009783","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:56.047Z"}
{"level":"debug","message":"Tercero actualizado: 830009783","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:56.426Z"}
{"level":"debug","message":"Procesando tercero: 830113831","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:56.426Z"}
{"level":"debug","message":"Tercero actualizado: 830113831","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:56.791Z"}
{"level":"debug","message":"Procesando tercero: 800256161","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:56.791Z"}
{"level":"debug","message":"Tercero actualizado: 800256161","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:57.158Z"}
{"level":"debug","message":"Procesando tercero: 830003564","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:57.159Z"}
{"level":"debug","message":"Tercero actualizado: 830003564","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:57.553Z"}
{"level":"debug","message":"Procesando tercero: 899999239","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:57.553Z"}
{"level":"debug","message":"Tercero actualizado: 899999239","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:57.923Z"}
{"level":"debug","message":"Procesando tercero: 800227940","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:57.924Z"}
{"level":"debug","message":"Tercero actualizado: 800227940","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:58.299Z"}
{"level":"debug","message":"Procesando tercero: 890303208","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:58.299Z"}
{"level":"debug","message":"Tercero actualizado: 890303208","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:58.651Z"}
{"level":"debug","message":"Procesando tercero: 890303093","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:58.652Z"}
{"level":"debug","message":"Tercero actualizado: 890303093","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:59.027Z"}
{"level":"debug","message":"Procesando tercero: 800226175","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:59.028Z"}
{"level":"debug","message":"Tercero actualizado: 800226175","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:59.384Z"}
{"level":"info","message":"Procesando lote 3 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:59.497Z"}
{"level":"debug","message":"Procesando tercero: 800130907","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:59.497Z"}
{"level":"debug","message":"Tercero actualizado: 800130907","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:59.854Z"}
{"level":"debug","message":"Procesando tercero: 899999034","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:27:59.854Z"}
{"level":"debug","message":"Tercero actualizado: 899999034","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:00.201Z"}
{"level":"debug","message":"Procesando tercero: 22222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:00.201Z"}
{"level":"debug","message":"Tercero actualizado: 22222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:00.550Z"}
{"level":"debug","message":"Procesando tercero: 31712653","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:00.550Z"}
{"level":"debug","message":"Tercero insertado: 31712653","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:00.972Z"}
{"level":"debug","message":"Procesando tercero: 3173830671","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:00.973Z"}
{"level":"debug","message":"Tercero insertado: 3173830671","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:01.357Z"}
{"level":"debug","message":"Procesando tercero: 3185406572","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:01.357Z"}
{"level":"debug","message":"Tercero insertado: 3185406572","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:01.715Z"}
{"level":"debug","message":"Procesando tercero: 31896726","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:01.715Z"}
{"level":"debug","message":"Tercero insertado: 31896726","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:02.084Z"}
{"level":"debug","message":"Procesando tercero: 31945493","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:02.084Z"}
{"level":"debug","message":"Tercero insertado: 31945493","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:02.451Z"}
{"level":"debug","message":"Procesando tercero: 31980896","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:02.451Z"}
{"level":"debug","message":"Tercero insertado: 31980896","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:02.821Z"}
{"level":"debug","message":"Procesando tercero: 3217849609","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:02.821Z"}
{"level":"debug","message":"Tercero insertado: 3217849609","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:03.178Z"}
{"level":"info","message":"Procesando lote 4 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:03.282Z"}
{"level":"debug","message":"Procesando tercero: 36993207","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:03.283Z"}
{"level":"debug","message":"Tercero insertado: 36993207","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:03.652Z"}
{"level":"debug","message":"Procesando tercero: 38610507","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:03.653Z"}
{"level":"debug","message":"Tercero insertado: 38610507","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:04.020Z"}
{"level":"debug","message":"Procesando tercero: 38868770","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:04.020Z"}
{"level":"debug","message":"Tercero insertado: 38868770","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:04.386Z"}
{"level":"debug","message":"Procesando tercero: 38940510","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:04.386Z"}
{"level":"debug","message":"Tercero insertado: 38940510","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:04.770Z"}
{"level":"debug","message":"Procesando tercero: 39638008","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:04.770Z"}
{"level":"debug","message":"Tercero insertado: 39638008","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:05.144Z"}
{"level":"debug","message":"Procesando tercero: 4287187","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:05.144Z"}
{"level":"debug","message":"Tercero insertado: 4287187","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:05.513Z"}
{"level":"debug","message":"Procesando tercero: 51858523","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:05.513Z"}
{"level":"debug","message":"Tercero insertado: 51858523","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:05.871Z"}
{"level":"debug","message":"Procesando tercero: 5244233","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:05.871Z"}
{"level":"debug","message":"Tercero insertado: 5244233","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:06.249Z"}
{"level":"debug","message":"Procesando tercero: 6136260","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:06.249Z"}
{"level":"debug","message":"Tercero insertado: 6136260","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:06.601Z"}
{"level":"debug","message":"Procesando tercero: 6136491","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:06.602Z"}
{"level":"debug","message":"Tercero insertado: 6136491","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:06.949Z"}
{"level":"info","message":"Procesando lote 5 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:07.062Z"}
{"level":"debug","message":"Procesando tercero: 6166738","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:07.063Z"}
{"level":"debug","message":"Tercero insertado: 6166738","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:07.445Z"}
{"level":"debug","message":"Procesando tercero: 6184934","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:07.445Z"}
{"level":"debug","message":"Tercero insertado: 6184934","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:07.808Z"}
{"level":"debug","message":"Procesando tercero: 6190941","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:07.808Z"}
{"level":"debug","message":"Tercero insertado: 6190941","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:08.191Z"}
{"level":"debug","message":"Procesando tercero: 6245756","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:08.191Z"}
{"level":"debug","message":"Tercero insertado: 6245756","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:08.549Z"}
{"level":"debug","message":"Procesando tercero: 6245797","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:08.550Z"}
{"level":"debug","message":"Tercero insertado: 6245797","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:08.905Z"}
{"level":"debug","message":"Procesando tercero: 6245822","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:08.905Z"}
{"level":"debug","message":"Tercero insertado: 6245822","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:09.284Z"}
{"level":"debug","message":"Procesando tercero: 6246422","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:09.285Z"}
{"level":"debug","message":"Tercero insertado: 6246422","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:09.650Z"}
{"level":"debug","message":"Procesando tercero: 6246706","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:09.650Z"}
{"level":"debug","message":"Tercero insertado: 6246706","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:10.027Z"}
{"level":"debug","message":"Procesando tercero: 6246943","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:10.027Z"}
{"level":"debug","message":"Tercero insertado: 6246943","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:10.401Z"}
{"level":"debug","message":"Procesando tercero: 6247862","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:10.401Z"}
{"level":"debug","message":"Tercero insertado: 6247862","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:10.750Z"}
{"level":"info","message":"Procesando lote 6 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:10.858Z"}
{"level":"debug","message":"Procesando tercero: 6248276","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:10.858Z"}
{"level":"debug","message":"Tercero insertado: 6248276","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:11.217Z"}
{"level":"debug","message":"Procesando tercero: 6248348","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:11.218Z"}
{"level":"debug","message":"Tercero insertado: 6248348","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:11.569Z"}
{"level":"debug","message":"Procesando tercero: 6248470","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:11.570Z"}
{"level":"debug","message":"Tercero insertado: 6248470","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:11.934Z"}
{"level":"debug","message":"Procesando tercero: 6248844","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:11.934Z"}
{"level":"debug","message":"Tercero insertado: 6248844","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:12.287Z"}
{"level":"debug","message":"Procesando tercero: 6251226","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:12.287Z"}
{"level":"debug","message":"Tercero insertado: 6251226","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:12.685Z"}
{"level":"debug","message":"Procesando tercero: 6252157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:12.685Z"}
{"level":"debug","message":"Tercero insertado: 6252157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:13.033Z"}
{"level":"debug","message":"Procesando tercero: 6258180","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:13.033Z"}
{"level":"debug","message":"Tercero insertado: 6258180","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:13.382Z"}
{"level":"debug","message":"Procesando tercero: 6260127","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:13.382Z"}
{"level":"debug","message":"Tercero insertado: 6260127","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:13.754Z"}
{"level":"debug","message":"Procesando tercero: 6260183","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:13.755Z"}
{"level":"debug","message":"Tercero insertado: 6260183","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:14.114Z"}
{"level":"debug","message":"Procesando tercero: 6311820","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:14.114Z"}
{"level":"debug","message":"Tercero insertado: 6311820","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:14.480Z"}
{"level":"info","message":"Procesando lote 7 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:14.587Z"}
{"level":"debug","message":"Procesando tercero: 6341751","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:14.588Z"}
{"level":"debug","message":"Tercero insertado: 6341751","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:14.942Z"}
{"level":"debug","message":"Procesando tercero: 6464447","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:14.942Z"}
{"level":"debug","message":"Tercero insertado: 6464447","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:15.292Z"}
{"level":"debug","message":"Procesando tercero: 66852987","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:15.292Z"}
{"level":"debug","message":"Tercero insertado: 66852987","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:15.659Z"}
{"level":"debug","message":"Procesando tercero: 66887082","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:15.659Z"}
{"level":"debug","message":"Tercero insertado: 66887082","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:16.026Z"}
{"level":"debug","message":"Procesando tercero: 66887975","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:16.026Z"}
{"level":"debug","message":"Tercero insertado: 66887975","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:16.451Z"}
{"level":"debug","message":"Procesando tercero: 66910544","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:16.451Z"}
{"level":"debug","message":"Tercero insertado: 66910544","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:16.814Z"}
{"level":"debug","message":"Procesando tercero: 66911910","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:16.815Z"}
{"level":"debug","message":"Tercero insertado: 66911910","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:17.178Z"}
{"level":"debug","message":"Procesando tercero: 66912745","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:17.179Z"}
{"level":"debug","message":"Tercero insertado: 66912745","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:17.586Z"}
{"level":"debug","message":"Procesando tercero: 66913044","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:17.587Z"}
{"level":"debug","message":"Tercero insertado: 66913044","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:17.983Z"}
{"level":"debug","message":"Procesando tercero: 66913183","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:17.984Z"}
{"level":"debug","message":"Tercero insertado: 66913183","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:18.332Z"}
{"level":"info","message":"Procesando lote 8 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:18.443Z"}
{"level":"debug","message":"Procesando tercero: 66913261","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:18.443Z"}
{"level":"debug","message":"Tercero insertado: 66913261","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:18.801Z"}
{"level":"debug","message":"Procesando tercero: 66913318","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:18.801Z"}
{"level":"debug","message":"Tercero insertado: 66913318","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:19.162Z"}
{"level":"debug","message":"Procesando tercero: 66913377","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:19.162Z"}
{"level":"debug","message":"Tercero insertado: 66913377","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:19.509Z"}
{"level":"debug","message":"Procesando tercero: 67038489","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:19.509Z"}
{"level":"debug","message":"Tercero insertado: 67038489","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:19.861Z"}
{"level":"debug","message":"Procesando tercero: 700220927","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:19.861Z"}
{"level":"debug","message":"Tercero insertado: 700220927","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:20.239Z"}
{"level":"debug","message":"Procesando tercero: 70827189","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:20.239Z"}
{"level":"debug","message":"Tercero insertado: 70827189","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:20.589Z"}
{"level":"debug","message":"Procesando tercero: 70829779","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:20.589Z"}
{"level":"debug","message":"Tercero insertado: 70829779","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:20.956Z"}
{"level":"debug","message":"Procesando tercero: 70902689","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:20.956Z"}
{"level":"debug","message":"Tercero insertado: 70902689","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:21.306Z"}
{"level":"debug","message":"Procesando tercero: 75002853","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:21.306Z"}
{"level":"debug","message":"Tercero insertado: 75002853","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:21.671Z"}
{"level":"debug","message":"Procesando tercero: 76325007","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:21.671Z"}
{"level":"debug","message":"Tercero insertado: 76325007","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:22.055Z"}
{"level":"info","message":"Procesando lote 9 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:22.171Z"}
{"level":"debug","message":"Procesando tercero: 79331347","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:22.171Z"}
{"level":"debug","message":"Tercero insertado: 79331347","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:22.522Z"}
{"level":"debug","message":"Procesando tercero: 800109274","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:22.523Z"}
{"level":"debug","message":"Tercero insertado: 800109274","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:22.880Z"}
{"level":"debug","message":"Procesando tercero: 800110994","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:22.880Z"}
{"level":"debug","message":"Tercero insertado: 800110994","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:23.240Z"}
{"level":"debug","message":"Procesando tercero: 800153993","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:23.240Z"}
{"level":"debug","message":"Tercero insertado: 800153993","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:23.606Z"}
{"level":"debug","message":"Procesando tercero: 800175166","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:23.607Z"}
{"level":"debug","message":"Tercero insertado: 800175166","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:23.972Z"}
{"level":"debug","message":"Procesando tercero: 800197268","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:23.972Z"}
{"level":"debug","message":"Tercero insertado: 800197268","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:24.327Z"}
{"level":"debug","message":"Procesando tercero: 800197463","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:24.327Z"}
{"level":"debug","message":"Tercero insertado: 800197463","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:24.687Z"}
{"level":"debug","message":"Procesando tercero: 800240674","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:24.687Z"}
{"level":"debug","message":"Tercero insertado: 800240674","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:25.075Z"}
{"level":"debug","message":"Procesando tercero: 800249860","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:25.075Z"}
{"level":"debug","message":"Tercero insertado: 800249860","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:25.453Z"}
{"level":"debug","message":"Procesando tercero: 805008738","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:25.453Z"}
{"level":"debug","message":"Tercero insertado: 805008738","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:25.807Z"}
{"level":"info","message":"Procesando lote 10 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:25.911Z"}
{"level":"debug","message":"Procesando tercero: 805025669","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:25.911Z"}
{"level":"debug","message":"Tercero insertado: 805025669","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:26.271Z"}
{"level":"debug","message":"Procesando tercero: 805028041","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:26.272Z"}
{"level":"debug","message":"Tercero insertado: 805028041","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:26.627Z"}
{"level":"debug","message":"Procesando tercero: 805028743","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:26.627Z"}
{"level":"debug","message":"Tercero insertado: 805028743","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:27.005Z"}
{"level":"debug","message":"Procesando tercero: 809009050","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:27.005Z"}
{"level":"debug","message":"Tercero insertado: 809009050","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:27.378Z"}
{"level":"debug","message":"Procesando tercero: 815001444","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:27.378Z"}
{"level":"debug","message":"Tercero insertado: 815001444","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:27.738Z"}
{"level":"debug","message":"Procesando tercero: 821003541","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:27.739Z"}
{"level":"debug","message":"Tercero insertado: 821003541","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:28.091Z"}
{"level":"debug","message":"Procesando tercero: 830006735","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:28.091Z"}
{"level":"debug","message":"Tercero insertado: 830006735","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:28.434Z"}
{"level":"debug","message":"Procesando tercero: 830045684","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:28.434Z"}
{"level":"debug","message":"Tercero insertado: 830045684","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:28.790Z"}
{"level":"debug","message":"Procesando tercero: 8300814071","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:28.791Z"}
{"level":"debug","message":"Tercero insertado: 8300814071","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:29.160Z"}
{"level":"debug","message":"Procesando tercero: 830085655","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:29.161Z"}
{"level":"debug","message":"Tercero insertado: 830085655","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:29.514Z"}
{"level":"info","message":"Procesando lote 11 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:29.627Z"}
{"level":"debug","message":"Procesando tercero: 83239225","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:29.627Z"}
{"level":"debug","message":"Tercero insertado: 83239225","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:29.991Z"}
{"level":"debug","message":"Procesando tercero: 845747444","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:29.991Z"}
{"level":"debug","message":"Tercero insertado: 845747444","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:30.364Z"}
{"level":"debug","message":"Procesando tercero: 85120433","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:30.365Z"}
{"level":"debug","message":"Tercero insertado: 85120433","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:30.734Z"}
{"level":"debug","message":"Procesando tercero: 860000122","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:30.734Z"}
{"level":"debug","message":"Tercero insertado: 860000122","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:31.079Z"}
{"level":"debug","message":"Procesando tercero: 860000135","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:31.079Z"}
{"level":"debug","message":"Tercero insertado: 860000135","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:31.437Z"}
{"level":"debug","message":"Procesando tercero: 860000258","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:31.438Z"}
{"level":"debug","message":"Tercero insertado: 860000258","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:31.789Z"}
{"level":"debug","message":"Procesando tercero: 860000261","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:31.789Z"}
{"level":"debug","message":"Tercero insertado: 860000261","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:32.155Z"}
{"level":"debug","message":"Procesando tercero: 860003831","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:32.155Z"}
{"level":"debug","message":"Tercero insertado: 860003831","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:32.517Z"}
{"level":"debug","message":"Procesando tercero: 860013771","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:32.517Z"}
{"level":"debug","message":"Tercero insertado: 860013771","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:32.864Z"}
{"level":"debug","message":"Procesando tercero: 860031606","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:32.864Z"}
{"level":"debug","message":"Tercero insertado: 860031606","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:33.217Z"}
{"level":"info","message":"Procesando lote 12 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:33.323Z"}
{"level":"debug","message":"Procesando tercero: 860031786","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:33.324Z"}
{"level":"debug","message":"Tercero insertado: 860031786","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:33.678Z"}
{"level":"debug","message":"Procesando tercero: 860037013","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:33.679Z"}
{"level":"debug","message":"Tercero insertado: 860037013","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:34.041Z"}
{"level":"debug","message":"Procesando tercero: 860049042","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:34.042Z"}
{"level":"debug","message":"Tercero insertado: 860049042","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:34.428Z"}
{"level":"debug","message":"Procesando tercero: 860512330","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:34.428Z"}
{"level":"debug","message":"Tercero insertado: 860512330","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:34.790Z"}
{"level":"debug","message":"Procesando tercero: 860515802","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:34.790Z"}
{"level":"debug","message":"Tercero insertado: 860515802","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:35.167Z"}
{"level":"debug","message":"Procesando tercero: 860521637","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:35.167Z"}
{"level":"debug","message":"Tercero insertado: 860521637","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:35.561Z"}
{"level":"debug","message":"Procesando tercero: 8821258","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:35.561Z"}
{"level":"debug","message":"Tercero insertado: 8821258","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:35.906Z"}
{"level":"debug","message":"Procesando tercero: 890110964","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:35.906Z"}
{"level":"debug","message":"Tercero insertado: 890110964","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:36.282Z"}
{"level":"debug","message":"Procesando tercero: 890300208","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:36.283Z"}
{"level":"debug","message":"Tercero insertado: 890300208","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:36.636Z"}
{"level":"debug","message":"Procesando tercero: 890300466","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:36.636Z"}
{"level":"debug","message":"Tercero insertado: 890300466","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:36.999Z"}
{"level":"info","message":"Procesando lote 13 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:37.101Z"}
{"level":"debug","message":"Procesando tercero: 890308898","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:37.102Z"}
{"level":"debug","message":"Tercero insertado: 890308898","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:37.466Z"}
{"level":"debug","message":"Procesando tercero: 890310276","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:37.466Z"}
{"level":"debug","message":"Tercero insertado: 890310276","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:37.836Z"}
{"level":"debug","message":"Procesando tercero: 890311629","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:37.836Z"}
{"level":"debug","message":"Tercero insertado: 890311629","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:38.199Z"}
{"level":"debug","message":"Procesando tercero: 890316938","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:38.199Z"}
{"level":"debug","message":"Tercero insertado: 890316938","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:38.567Z"}
{"level":"debug","message":"Procesando tercero: 890317016","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:38.568Z"}
{"level":"debug","message":"Tercero insertado: 890317016","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:38.921Z"}
{"level":"debug","message":"Procesando tercero: 890319112","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:38.922Z"}
{"level":"debug","message":"Tercero insertado: 890319112","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:39.299Z"}
{"level":"debug","message":"Procesando tercero: 890321052","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:39.300Z"}
{"level":"debug","message":"Tercero insertado: 890321052","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:39.653Z"}
{"level":"debug","message":"Procesando tercero: 890325198","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:39.654Z"}
{"level":"debug","message":"Tercero insertado: 890325198","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:40.008Z"}
{"level":"debug","message":"Procesando tercero: 890399032","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:40.008Z"}
{"level":"debug","message":"Tercero insertado: 890399032","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:40.402Z"}
{"level":"debug","message":"Procesando tercero: 890700058","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:40.402Z"}
{"level":"debug","message":"Tercero insertado: 890700058","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:40.750Z"}
{"level":"info","message":"Procesando lote 14 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:40.858Z"}
{"level":"debug","message":"Procesando tercero: 890800718","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:40.858Z"}
{"level":"debug","message":"Tercero insertado: 890800718","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:41.228Z"}
{"level":"debug","message":"Procesando tercero: 890900608","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:41.228Z"}
{"level":"debug","message":"Tercero insertado: 890900608","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:41.583Z"}
{"level":"debug","message":"Procesando tercero: *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:41.583Z"}
{"level":"debug","message":"Tercero insertado: *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:41.939Z"}
{"level":"debug","message":"Procesando tercero: 890903407","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:41.939Z"}
{"level":"debug","message":"Tercero insertado: 890903407","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:42.317Z"}
{"level":"debug","message":"Procesando tercero: 890903939","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:42.317Z"}
{"level":"debug","message":"Tercero insertado: 890903939","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:42.679Z"}
{"level":"debug","message":"Procesando tercero: 890904478","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:42.679Z"}
{"level":"debug","message":"Tercero insertado: 890904478","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:43.035Z"}
{"level":"debug","message":"Procesando tercero: 891100445","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:43.036Z"}
{"level":"debug","message":"Tercero insertado: 891100445","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:43.400Z"}
{"level":"debug","message":"Procesando tercero: 891900492","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:43.400Z"}
{"level":"debug","message":"Tercero insertado: 891900492","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:43.750Z"}
{"level":"debug","message":"Procesando tercero: 895544444","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:43.751Z"}
{"level":"debug","message":"Tercero insertado: 895544444","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:44.106Z"}
{"level":"debug","message":"Procesando tercero: 900081357","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:44.107Z"}
{"level":"debug","message":"Tercero insertado: 900081357","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:44.477Z"}
{"level":"info","message":"Procesando lote 15 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:44.590Z"}
{"level":"debug","message":"Procesando tercero: 900093199","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:44.590Z"}
{"level":"debug","message":"Tercero insertado: 900093199","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:44.974Z"}
{"level":"debug","message":"Procesando tercero: 900161802","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:44.975Z"}
{"level":"debug","message":"Tercero insertado: 900161802","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:45.366Z"}
{"level":"debug","message":"Procesando tercero: 900232474","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:45.366Z"}
{"level":"debug","message":"Tercero insertado: 900232474","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:45.738Z"}
{"level":"debug","message":"Procesando tercero: 900242912","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:45.738Z"}
{"level":"debug","message":"Tercero insertado: 900242912","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:46.100Z"}
{"level":"debug","message":"Procesando tercero: 900247483","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:46.100Z"}
{"level":"debug","message":"Tercero insertado: 900247483","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:46.467Z"}
{"level":"debug","message":"Procesando tercero: 900250345","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:46.467Z"}
{"level":"debug","message":"Tercero insertado: 900250345","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:46.814Z"}
{"level":"debug","message":"Procesando tercero: 900290777","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:46.814Z"}
{"level":"debug","message":"Tercero insertado: 900290777","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:47.173Z"}
{"level":"debug","message":"Procesando tercero: 900293086","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:47.173Z"}
{"level":"debug","message":"Tercero insertado: 900293086","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:47.544Z"}
{"level":"debug","message":"Procesando tercero: 900311359","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:47.545Z"}
{"level":"debug","message":"Tercero insertado: 900311359","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:47.998Z"}
{"level":"debug","message":"Procesando tercero: 900316197","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:47.998Z"}
{"level":"debug","message":"Tercero insertado: 900316197","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:48.349Z"}
{"level":"info","message":"Procesando lote 16 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:48.453Z"}
{"level":"debug","message":"Procesando tercero: *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:48.454Z"}
{"level":"debug","message":"Tercero insertado: *********","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:48.812Z"}
{"level":"debug","message":"Procesando tercero: 900341086","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:48.813Z"}
{"level":"debug","message":"Tercero insertado: 900341086","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:49.176Z"}
{"level":"debug","message":"Procesando tercero: 900409870","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:49.176Z"}
{"level":"debug","message":"Tercero insertado: 900409870","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:49.554Z"}
{"level":"debug","message":"Procesando tercero: 900434884","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:49.554Z"}
{"level":"debug","message":"Tercero insertado: 900434884","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:49.911Z"}
{"level":"debug","message":"Procesando tercero: 900539631","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:49.912Z"}
{"level":"debug","message":"Tercero insertado: 900539631","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:50.279Z"}
{"level":"debug","message":"Procesando tercero: 900587259","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:50.279Z"}
{"level":"debug","message":"Tercero insertado: 900587259","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:50.631Z"}
{"level":"debug","message":"Procesando tercero: 900691476","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:50.631Z"}
{"level":"debug","message":"Tercero insertado: 900691476","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:50.998Z"}
{"level":"debug","message":"Procesando tercero: 900782379","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:50.998Z"}
{"level":"debug","message":"Tercero insertado: 900782379","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:51.365Z"}
{"level":"debug","message":"Procesando tercero: 900818921","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:51.365Z"}
{"level":"debug","message":"Tercero insertado: 900818921","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:51.735Z"}
{"level":"debug","message":"Procesando tercero: 900825196","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:51.735Z"}
{"level":"debug","message":"Tercero insertado: 900825196","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:52.106Z"}
{"level":"info","message":"Procesando lote 17 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:52.219Z"}
{"level":"debug","message":"Procesando tercero: 900930798","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:52.220Z"}
{"level":"debug","message":"Tercero insertado: 900930798","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:52.595Z"}
{"level":"debug","message":"Procesando tercero: 901006683","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:52.595Z"}
{"level":"debug","message":"Tercero insertado: 901006683","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:52.962Z"}
{"level":"debug","message":"Procesando tercero: 901050056","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:52.963Z"}
{"level":"debug","message":"Tercero insertado: 901050056","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:53.339Z"}
{"level":"debug","message":"Procesando tercero: 901102311","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:53.339Z"}
{"level":"debug","message":"Tercero insertado: 901102311","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:53.707Z"}
{"level":"debug","message":"Procesando tercero: 901142907","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:53.707Z"}
{"level":"debug","message":"Tercero insertado: 901142907","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:54.077Z"}
{"level":"debug","message":"Procesando tercero: 901154147","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:54.078Z"}
{"level":"debug","message":"Tercero insertado: 901154147","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:54.451Z"}
{"level":"debug","message":"Procesando tercero: 901179238","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:54.452Z"}
{"level":"debug","message":"Tercero insertado: 901179238","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:54.834Z"}
{"level":"debug","message":"Procesando tercero: 901220943","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:54.835Z"}
{"level":"debug","message":"Tercero insertado: 901220943","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:55.260Z"}
{"level":"debug","message":"Procesando tercero: 901262283","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:55.261Z"}
{"level":"debug","message":"Tercero insertado: 901262283","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:55.631Z"}
{"level":"debug","message":"Procesando tercero: 901266118","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:55.631Z"}
{"level":"debug","message":"Tercero insertado: 901266118","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:55.991Z"}
{"level":"info","message":"Procesando lote 18 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:56.094Z"}
{"level":"debug","message":"Procesando tercero: 901275422","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:56.094Z"}
{"level":"debug","message":"Tercero insertado: 901275422","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:56.483Z"}
{"level":"debug","message":"Procesando tercero: 901330169","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:56.484Z"}
{"level":"debug","message":"Tercero insertado: 901330169","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:56.846Z"}
{"level":"debug","message":"Procesando tercero: 91309492","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:56.846Z"}
{"level":"debug","message":"Tercero insertado: 91309492","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:57.206Z"}
{"level":"debug","message":"Procesando tercero: 94045057","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:57.207Z"}
{"level":"debug","message":"Tercero insertado: 94045057","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:57.579Z"}
{"level":"debug","message":"Procesando tercero: 94192730","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:57.579Z"}
{"level":"debug","message":"Tercero insertado: 94192730","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:57.977Z"}
{"level":"debug","message":"Procesando tercero: 94268166","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:57.977Z"}
{"level":"debug","message":"Tercero insertado: 94268166","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:58.347Z"}
{"level":"debug","message":"Procesando tercero: 94269950","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:58.347Z"}
{"level":"debug","message":"Tercero insertado: 94269950","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:58.714Z"}
{"level":"debug","message":"Procesando tercero: 94374887","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:58.714Z"}
{"level":"debug","message":"Tercero insertado: 94374887","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:59.094Z"}
{"level":"debug","message":"Procesando tercero: 94379979","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:59.094Z"}
{"level":"debug","message":"Tercero insertado: 94379979","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:59.449Z"}
{"level":"debug","message":"Procesando tercero: 94397450","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:59.450Z"}
{"level":"debug","message":"Tercero insertado: 94397450","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:59.829Z"}
{"level":"info","message":"Procesando lote 19 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:59.941Z"}
{"level":"debug","message":"Procesando tercero: 9441187","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:28:59.942Z"}
{"level":"debug","message":"Tercero insertado: 9441187","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:00.318Z"}
{"level":"debug","message":"Procesando tercero: 94421720","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:00.318Z"}
{"level":"debug","message":"Tercero insertado: 94421720","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:00.702Z"}
{"level":"debug","message":"Procesando tercero: 94422175","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:00.702Z"}
{"level":"debug","message":"Tercero insertado: 94422175","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:01.082Z"}
{"level":"debug","message":"Procesando tercero: 94423254","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:01.082Z"}
{"level":"debug","message":"Tercero insertado: 94423254","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:01.458Z"}
{"level":"debug","message":"Procesando tercero: 94426783","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:01.458Z"}
{"level":"debug","message":"Tercero insertado: 94426783","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:01.846Z"}
{"level":"debug","message":"Procesando tercero: 97445923","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:01.846Z"}
{"level":"debug","message":"Tercero insertado: 97445923","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:02.236Z"}
{"level":"debug","message":"Procesando tercero: 98349184","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:02.236Z"}
{"level":"debug","message":"Tercero insertado: 98349184","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:02.607Z"}
{"level":"debug","message":"Procesando tercero: 98367122","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:02.607Z"}
{"level":"debug","message":"Tercero insertado: 98367122","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:02.975Z"}
{"level":"debug","message":"Procesando tercero: 98535322","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:02.975Z"}
{"level":"debug","message":"Tercero insertado: 98535322","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:03.423Z"}
{"level":"debug","message":"Procesando tercero: 98549189","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:03.423Z"}
{"level":"debug","message":"Tercero insertado: 98549189","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:03.833Z"}
{"level":"info","message":"Procesando lote 20 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:03.943Z"}
{"level":"debug","message":"Procesando tercero: 1114734140","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:03.943Z"}
{"level":"debug","message":"Tercero insertado: 1114734140","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:04.393Z"}
{"level":"debug","message":"Procesando tercero: 1034559","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:04.393Z"}
{"level":"debug","message":"Tercero insertado: 1034559","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:04.764Z"}
{"level":"debug","message":"Procesando tercero: 901251427","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:04.764Z"}
{"level":"debug","message":"Tercero insertado: 901251427","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:05.145Z"}
{"level":"debug","message":"Procesando tercero: 6248275","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:05.145Z"}
{"level":"debug","message":"Tercero insertado: 6248275","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:05.535Z"}
{"level":"debug","message":"Procesando tercero: 66910617","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:05.535Z"}
{"level":"debug","message":"Tercero insertado: 66910617","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:05.909Z"}
{"level":"debug","message":"Procesando tercero: 013","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:05.910Z"}
{"level":"debug","message":"Tercero insertado: 013","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:06.298Z"}
{"level":"debug","message":"Procesando tercero: 1114734410","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:06.298Z"}
{"level":"debug","message":"Tercero insertado: 1114734410","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:06.659Z"}
{"level":"debug","message":"Procesando tercero: 901321529","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:06.659Z"}
{"level":"debug","message":"Tercero insertado: 901321529","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:07.054Z"}
{"level":"debug","message":"Procesando tercero: 901264865","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:07.054Z"}
{"level":"debug","message":"Tercero insertado: 901264865","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:07.428Z"}
{"level":"debug","message":"Procesando tercero: 12930651","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:07.429Z"}
{"level":"debug","message":"Tercero insertado: 12930651","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:07.809Z"}
{"level":"info","message":"Procesando lote 21 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:07.913Z"}
{"level":"debug","message":"Procesando tercero: 900518605","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:07.914Z"}
{"level":"debug","message":"Tercero insertado: 900518605","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:08.276Z"}
{"level":"debug","message":"Procesando tercero: 11144158739","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:08.276Z"}
{"level":"debug","message":"Tercero insertado: 11144158739","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:08.639Z"}
{"level":"debug","message":"Procesando tercero: 805009584","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:08.639Z"}
{"level":"debug","message":"Tercero insertado: 805009584","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:09.028Z"}
{"level":"debug","message":"Procesando tercero: 18155327","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:09.028Z"}
{"level":"debug","message":"Tercero insertado: 18155327","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:09.389Z"}
{"level":"debug","message":"Procesando tercero: 14985276","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:09.389Z"}
{"level":"debug","message":"Tercero insertado: 14985276","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:09.743Z"}
{"level":"debug","message":"Procesando tercero: 6064073","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:09.743Z"}
{"level":"debug","message":"Tercero insertado: 6064073","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:10.114Z"}
{"level":"debug","message":"Procesando tercero: 860063952","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:10.114Z"}
{"level":"debug","message":"Tercero insertado: 860063952","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:10.484Z"}
{"level":"debug","message":"Procesando tercero: 1086920971","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:10.485Z"}
{"level":"debug","message":"Tercero insertado: 1086920971","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:10.849Z"}
{"level":"debug","message":"Procesando tercero: 94467087","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:10.850Z"}
{"level":"debug","message":"Tercero insertado: 94467087","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:11.220Z"}
{"level":"debug","message":"Procesando tercero: 94420694","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:11.220Z"}
{"level":"debug","message":"Tercero insertado: 94420694","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:11.597Z"}
{"level":"info","message":"Procesando lote 22 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:11.698Z"}
{"level":"debug","message":"Procesando tercero: 901399930","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:11.698Z"}
{"level":"debug","message":"Tercero insertado: 901399930","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:12.059Z"}
{"level":"debug","message":"Procesando tercero: 34638854","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:12.059Z"}
{"level":"debug","message":"Tercero insertado: 34638854","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:12.420Z"}
{"level":"debug","message":"Procesando tercero: 8636030","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:12.420Z"}
{"level":"debug","message":"Tercero insertado: 8636030","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:12.785Z"}
{"level":"debug","message":"Procesando tercero: 900372199","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:12.785Z"}
{"level":"debug","message":"Tercero insertado: 900372199","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:13.143Z"}
{"level":"debug","message":"Procesando tercero: 901268386","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:13.144Z"}
{"level":"debug","message":"Tercero insertado: 901268386","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:13.510Z"}
{"level":"debug","message":"Procesando tercero: 6248230","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:13.510Z"}
{"level":"debug","message":"Tercero insertado: 6248230","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:13.876Z"}
{"level":"debug","message":"Procesando tercero: 5244164","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:13.877Z"}
{"level":"debug","message":"Tercero insertado: 5244164","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:14.254Z"}
{"level":"debug","message":"Procesando tercero: 805028410","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:14.254Z"}
{"level":"debug","message":"Tercero insertado: 805028410","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:14.627Z"}
{"level":"debug","message":"Procesando tercero: 1114734715","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:14.627Z"}
{"level":"debug","message":"Tercero insertado: 1114734715","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:14.998Z"}
{"level":"debug","message":"Procesando tercero: 8568559","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:14.998Z"}
{"level":"debug","message":"Tercero insertado: 8568559","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:15.362Z"}
{"level":"info","message":"Procesando lote 23 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:15.469Z"}
{"level":"debug","message":"Procesando tercero: 16361380","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:15.469Z"}
{"level":"debug","message":"Tercero insertado: 16361380","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:15.828Z"}
{"level":"debug","message":"Procesando tercero: 1114733563","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:15.828Z"}
{"level":"debug","message":"Tercero insertado: 1114733563","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:16.185Z"}
{"level":"debug","message":"Procesando tercero: 14","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:16.186Z"}
{"level":"debug","message":"Tercero insertado: 14","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:16.545Z"}
{"level":"debug","message":"Procesando tercero: 1114730627","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:16.545Z"}
{"level":"debug","message":"Tercero insertado: 1114730627","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:16.941Z"}
{"level":"debug","message":"Procesando tercero: 1193101084","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:16.941Z"}
{"level":"debug","message":"Tercero insertado: 1193101084","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:17.347Z"}
{"level":"debug","message":"Procesando tercero: 29771997","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:17.347Z"}
{"level":"debug","message":"Tercero insertado: 29771997","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:17.738Z"}
{"level":"debug","message":"Procesando tercero: 1130596693","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:17.738Z"}
{"level":"debug","message":"Tercero insertado: 1130596693","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:18.118Z"}
{"level":"debug","message":"Procesando tercero: 6386790","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:18.118Z"}
{"level":"debug","message":"Tercero insertado: 6386790","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:18.484Z"}
{"level":"debug","message":"Procesando tercero: 1006228940","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:18.484Z"}
{"level":"debug","message":"Tercero insertado: 1006228940","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:18.884Z"}
{"level":"debug","message":"Procesando tercero: 1107082841","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:18.885Z"}
{"level":"debug","message":"Tercero insertado: 1107082841","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:19.295Z"}
{"level":"info","message":"Procesando lote 24 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:19.407Z"}
{"level":"debug","message":"Procesando tercero: 890399001","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:19.408Z"}
{"level":"debug","message":"Tercero insertado: 890399001","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:19.779Z"}
{"level":"debug","message":"Procesando tercero: 901032632","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:19.780Z"}
{"level":"debug","message":"Tercero insertado: 901032632","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:20.166Z"}
{"level":"debug","message":"Procesando tercero: 900936913","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:20.166Z"}
{"level":"debug","message":"Tercero insertado: 900936913","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:20.544Z"}
{"level":"debug","message":"Procesando tercero: 16635233","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:20.545Z"}
{"level":"debug","message":"Tercero insertado: 16635233","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:20.958Z"}
{"level":"debug","message":"Procesando tercero: 890903790","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:20.958Z"}
{"level":"debug","message":"Tercero insertado: 890903790","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:21.326Z"}
{"level":"debug","message":"Procesando tercero: 805011359","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:21.326Z"}
{"level":"debug","message":"Tercero insertado: 805011359","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:21.688Z"}
{"level":"debug","message":"Procesando tercero: 805016173","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:21.688Z"}
{"level":"debug","message":"Tercero insertado: 805016173","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:22.061Z"}
{"level":"debug","message":"Procesando tercero: 901384044","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:22.062Z"}
{"level":"debug","message":"Tercero insertado: 901384044","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:22.439Z"}
{"level":"debug","message":"Procesando tercero: 38670999","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:22.439Z"}
{"level":"debug","message":"Tercero insertado: 38670999","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:22.824Z"}
{"level":"debug","message":"Procesando tercero: 2450561","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:22.824Z"}
{"level":"debug","message":"Tercero insertado: 2450561","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:23.191Z"}
{"level":"info","message":"Procesando lote 25 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:23.295Z"}
{"level":"debug","message":"Procesando tercero: 66911379","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:23.295Z"}
{"level":"debug","message":"Tercero insertado: 66911379","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:23.669Z"}
{"level":"debug","message":"Procesando tercero: 1006217783","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:23.669Z"}
{"level":"debug","message":"Tercero insertado: 1006217783","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:24.026Z"}
{"level":"debug","message":"Procesando tercero: 800242106","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:24.026Z"}
{"level":"debug","message":"Tercero insertado: 800242106","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:24.395Z"}
{"level":"debug","message":"Procesando tercero: 1130627412","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:24.395Z"}
{"level":"debug","message":"Tercero insertado: 1130627412","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:24.768Z"}
{"level":"debug","message":"Procesando tercero: 900575363","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:24.769Z"}
{"level":"debug","message":"Tercero insertado: 900575363","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:25.143Z"}
{"level":"debug","message":"Procesando tercero: 3122554473","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:25.143Z"}
{"level":"debug","message":"Tercero insertado: 3122554473","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:25.531Z"}
{"level":"debug","message":"Procesando tercero: 909236625121989","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:25.532Z"}
{"level":"debug","message":"Tercero insertado: 909236625121989","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:25.912Z"}
{"level":"debug","message":"Procesando tercero: 900149755","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:25.912Z"}
{"level":"debug","message":"Tercero insertado: 900149755","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:26.349Z"}
{"level":"debug","message":"Procesando tercero: 901240490","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:26.349Z"}
{"level":"debug","message":"Tercero insertado: 901240490","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:26.719Z"}
{"level":"debug","message":"Procesando tercero: 94325118","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:26.720Z"}
{"level":"debug","message":"Tercero insertado: 94325118","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:27.080Z"}
{"level":"info","message":"Procesando lote 26 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:27.184Z"}
{"level":"debug","message":"Procesando tercero: 900734017","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:27.184Z"}
{"level":"debug","message":"Tercero insertado: 900734017","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:27.541Z"}
{"level":"debug","message":"Procesando tercero: 1114732670","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:27.542Z"}
{"level":"debug","message":"Tercero insertado: 1114732670","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:27.907Z"}
{"level":"debug","message":"Procesando tercero: 901061045","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:27.907Z"}
{"level":"debug","message":"Tercero insertado: 901061045","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:28.284Z"}
{"level":"debug","message":"Procesando tercero: 66913354","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:28.284Z"}
{"level":"debug","message":"Tercero insertado: 66913354","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:28.766Z"}
{"level":"debug","message":"Procesando tercero: 1143825697","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:28.766Z"}
{"level":"debug","message":"Tercero insertado: 1143825697","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:29.147Z"}
{"level":"debug","message":"Procesando tercero: 900863582","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:29.147Z"}
{"level":"debug","message":"Tercero insertado: 900863582","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:29.534Z"}
{"level":"debug","message":"Procesando tercero: 901176597","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:29.534Z"}
{"level":"debug","message":"Tercero insertado: 901176597","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:29.923Z"}
{"level":"debug","message":"Procesando tercero: 900609479","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:29.924Z"}
{"level":"debug","message":"Tercero insertado: 900609479","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:30.283Z"}
{"level":"debug","message":"Procesando tercero: 7563061","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:30.284Z"}
{"level":"debug","message":"Tercero insertado: 7563061","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:30.662Z"}
{"level":"debug","message":"Procesando tercero: 890312141","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:30.663Z"}
{"level":"debug","message":"Tercero insertado: 890312141","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:31.058Z"}
{"level":"info","message":"Procesando lote 27 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:31.174Z"}
{"level":"debug","message":"Procesando tercero: 14576371","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:31.174Z"}
{"level":"debug","message":"Tercero insertado: 14576371","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:31.535Z"}
{"level":"debug","message":"Procesando tercero: 16377965","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:31.535Z"}
{"level":"debug","message":"Tercero insertado: 16377965","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:31.916Z"}
{"level":"debug","message":"Procesando tercero: 1143992111","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:31.917Z"}
{"level":"debug","message":"Tercero insertado: 1143992111","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:32.283Z"}
{"level":"debug","message":"Procesando tercero: 900336004","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:32.283Z"}
{"level":"debug","message":"Tercero insertado: 900336004","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:32.666Z"}
{"level":"debug","message":"Procesando tercero: 123456","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:32.666Z"}
{"level":"debug","message":"Tercero insertado: 123456","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:33.161Z"}
{"level":"debug","message":"Procesando tercero: 901097473","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:33.161Z"}
{"level":"debug","message":"Tercero insertado: 901097473","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:33.519Z"}
{"level":"debug","message":"Procesando tercero: 9002267153","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:33.520Z"}
{"level":"debug","message":"Tercero insertado: 9002267153","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:33.888Z"}
{"level":"debug","message":"Procesando tercero: 900156264","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:33.888Z"}
{"level":"debug","message":"Tercero insertado: 900156264","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:34.271Z"}
{"level":"debug","message":"Procesando tercero: 66651165","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:34.271Z"}
{"level":"debug","message":"Tercero insertado: 66651165","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:34.636Z"}
{"level":"debug","message":"Procesando tercero: 66858380","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:34.636Z"}
{"level":"debug","message":"Tercero insertado: 66858380","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:35.045Z"}
{"level":"info","message":"Procesando lote 28 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:35.150Z"}
{"level":"debug","message":"Procesando tercero: 6247132","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:35.150Z"}
{"level":"debug","message":"Tercero insertado: 6247132","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:35.521Z"}
{"level":"debug","message":"Procesando tercero: 14576495","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:35.521Z"}
{"level":"debug","message":"Tercero insertado: 14576495","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:35.882Z"}
{"level":"debug","message":"Procesando tercero: 800084003","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:35.882Z"}
{"level":"debug","message":"Tercero insertado: 800084003","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:36.273Z"}
{"level":"debug","message":"Procesando tercero: 901405358","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:36.273Z"}
{"level":"debug","message":"Tercero insertado: 901405358","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:36.629Z"}
{"level":"debug","message":"Procesando tercero: 1193543367","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:36.629Z"}
{"level":"debug","message":"Tercero insertado: 1193543367","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:37.002Z"}
{"level":"debug","message":"Procesando tercero: 29186556","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:37.002Z"}
{"level":"debug","message":"Tercero insertado: 29186556","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:37.367Z"}
{"level":"debug","message":"Procesando tercero: 901289383","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:37.367Z"}
{"level":"debug","message":"Tercero insertado: 901289383","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:37.736Z"}
{"level":"debug","message":"Procesando tercero: 1193209253","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:37.736Z"}
{"level":"debug","message":"Tercero insertado: 1193209253","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:38.091Z"}
{"level":"debug","message":"Procesando tercero: 1114735510","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:38.092Z"}
{"level":"debug","message":"Tercero insertado: 1114735510","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:38.455Z"}
{"level":"debug","message":"Procesando tercero: 94367337","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:38.455Z"}
{"level":"debug","message":"Tercero insertado: 94367337","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:38.811Z"}
{"level":"info","message":"Procesando lote 29 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:38.913Z"}
{"level":"debug","message":"Procesando tercero: 16749027","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:38.914Z"}
{"level":"debug","message":"Tercero insertado: 16749027","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:39.276Z"}
{"level":"debug","message":"Procesando tercero: 900993816","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:39.277Z"}
{"level":"debug","message":"Tercero insertado: 900993816","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:39.623Z"}
{"level":"debug","message":"Procesando tercero: 1151949839","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:39.624Z"}
{"level":"debug","message":"Tercero insertado: 1151949839","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:39.979Z"}
{"level":"debug","message":"Procesando tercero: 00015","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:39.979Z"}
{"level":"debug","message":"Tercero insertado: 00015","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:40.338Z"}
{"level":"debug","message":"Procesando tercero: 8600029644","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:40.338Z"}
{"level":"debug","message":"Tercero insertado: 8600029644","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:40.693Z"}
{"level":"debug","message":"Procesando tercero: 1193516166","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:40.693Z"}
{"level":"debug","message":"Tercero insertado: 1193516166","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:41.054Z"}
{"level":"debug","message":"Procesando tercero: 31995353","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:41.054Z"}
{"level":"debug","message":"Tercero insertado: 31995353","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:41.409Z"}
{"level":"debug","message":"Procesando tercero: 900398389","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:41.410Z"}
{"level":"debug","message":"Tercero insertado: 900398389","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:41.783Z"}
{"level":"debug","message":"Procesando tercero: 900397839","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:41.783Z"}
{"level":"debug","message":"Tercero insertado: 900397839","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:42.141Z"}
{"level":"debug","message":"Procesando tercero: 1114726143","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:42.141Z"}
{"level":"debug","message":"Tercero insertado: 1114726143","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:42.505Z"}
{"level":"info","message":"Procesando lote 30 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:42.608Z"}
{"level":"debug","message":"Procesando tercero: 1114735396","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:42.608Z"}
{"level":"debug","message":"Tercero insertado: 1114735396","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:43.007Z"}
{"level":"debug","message":"Procesando tercero: 12190876","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:43.008Z"}
{"level":"debug","message":"Tercero insertado: 12190876","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:43.375Z"}
{"level":"debug","message":"Procesando tercero: 900275698","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:43.375Z"}
{"level":"debug","message":"Tercero insertado: 900275698","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:43.743Z"}
{"level":"debug","message":"Procesando tercero: 890307367","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:43.743Z"}
{"level":"debug","message":"Tercero insertado: 890307367","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:44.096Z"}
{"level":"debug","message":"Procesando tercero: 901518790","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:44.096Z"}
{"level":"debug","message":"Tercero insertado: 901518790","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:44.466Z"}
{"level":"debug","message":"Procesando tercero: 20250434","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:44.466Z"}
{"level":"debug","message":"Tercero insertado: 20250434","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:44.821Z"}
{"level":"debug","message":"Procesando tercero: 901225955","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:44.821Z"}
{"level":"debug","message":"Tercero insertado: 901225955","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:45.196Z"}
{"level":"debug","message":"Procesando tercero: 900451027","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:45.196Z"}
{"level":"debug","message":"Tercero insertado: 900451027","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:45.548Z"}
{"level":"debug","message":"Procesando tercero: 1144055490","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:45.548Z"}
{"level":"debug","message":"Tercero insertado: 1144055490","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:45.894Z"}
{"level":"debug","message":"Procesando tercero: 900203566","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:45.894Z"}
{"level":"debug","message":"Tercero insertado: 900203566","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:46.281Z"}
{"level":"info","message":"Procesando lote 31 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:46.385Z"}
{"level":"debug","message":"Procesando tercero: 800224808","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:46.386Z"}
{"level":"debug","message":"Tercero insertado: 800224808","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:46.735Z"}
{"level":"debug","message":"Procesando tercero: 900226715","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:46.736Z"}
{"level":"debug","message":"Tercero insertado: 900226715","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:47.075Z"}
{"level":"debug","message":"Procesando tercero: 860066942","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:47.075Z"}
{"level":"debug","message":"Tercero insertado: 860066942","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:47.443Z"}
{"level":"debug","message":"Procesando tercero: 8001381881","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:47.443Z"}
{"level":"debug","message":"Tercero insertado: 8001381881","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:47.788Z"}
{"level":"debug","message":"Procesando tercero: 901130683","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:47.788Z"}
{"level":"debug","message":"Tercero insertado: 901130683","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:48.157Z"}
{"level":"debug","message":"Procesando tercero: 1114730792","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:48.157Z"}
{"level":"debug","message":"Tercero insertado: 1114730792","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:48.515Z"}
{"level":"debug","message":"Procesando tercero: 16598736","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:48.515Z"}
{"level":"debug","message":"Tercero insertado: 16598736","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:48.892Z"}
{"level":"debug","message":"Procesando tercero: 94488328","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:48.892Z"}
{"level":"debug","message":"Tercero insertado: 94488328","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:49.268Z"}
{"level":"debug","message":"Procesando tercero: 67010198","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:49.268Z"}
{"level":"debug","message":"Tercero insertado: 67010198","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:49.632Z"}
{"level":"debug","message":"Procesando tercero: 901212003","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:49.632Z"}
{"level":"debug","message":"Tercero insertado: 901212003","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:50.094Z"}
{"level":"info","message":"Procesando lote 32 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:50.205Z"}
{"level":"debug","message":"Procesando tercero: 16","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:50.205Z"}
{"level":"debug","message":"Tercero insertado: 16","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:50.559Z"}
{"level":"debug","message":"Procesando tercero: 18","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:50.559Z"}
{"level":"debug","message":"Tercero insertado: 18","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:50.939Z"}
{"level":"debug","message":"Procesando tercero: 19","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:50.939Z"}
{"level":"debug","message":"Tercero insertado: 19","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:51.331Z"}
{"level":"debug","message":"Procesando tercero: 901456035","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:51.331Z"}
{"level":"debug","message":"Tercero insertado: 901456035","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:51.679Z"}
{"level":"debug","message":"Procesando tercero: 80500157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:51.680Z"}
{"level":"debug","message":"Tercero insertado: 80500157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:52.043Z"}
{"level":"debug","message":"Procesando tercero: 8001704945","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:52.043Z"}
{"level":"debug","message":"Tercero insertado: 8001704945","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:52.394Z"}
{"level":"debug","message":"Procesando tercero: 830087479","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:52.394Z"}
{"level":"debug","message":"Tercero insertado: 830087479","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:52.753Z"}
{"level":"debug","message":"Procesando tercero: 805021560","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:52.753Z"}
{"level":"debug","message":"Tercero insertado: 805021560","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:53.114Z"}
{"level":"debug","message":"Procesando tercero: 1006209404","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:53.114Z"}
{"level":"debug","message":"Tercero insertado: 1006209404","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:53.487Z"}
{"level":"debug","message":"Procesando tercero: 1114733140","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:53.487Z"}
{"level":"debug","message":"Tercero insertado: 1114733140","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:53.865Z"}
{"level":"info","message":"Procesando lote 33 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:53.969Z"}
{"level":"debug","message":"Procesando tercero: 900257614","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:53.969Z"}
{"level":"debug","message":"Tercero insertado: 900257614","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:54.335Z"}
{"level":"debug","message":"Procesando tercero: 800100514","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:54.335Z"}
{"level":"debug","message":"Tercero insertado: 800100514","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:54.685Z"}
{"level":"debug","message":"Procesando tercero: 800045227","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:54.686Z"}
{"level":"debug","message":"Tercero insertado: 800045227","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:55.058Z"}
{"level":"debug","message":"Procesando tercero: 1113070522","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:55.058Z"}
{"level":"debug","message":"Tercero insertado: 1113070522","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:55.418Z"}
{"level":"debug","message":"Procesando tercero: 900932866","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:55.419Z"}
{"level":"debug","message":"Tercero insertado: 900932866","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:55.758Z"}
{"level":"debug","message":"Procesando tercero: 805020433","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:55.758Z"}
{"level":"debug","message":"Tercero insertado: 805020433","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:56.112Z"}
{"level":"debug","message":"Procesando tercero: 4939969","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:56.112Z"}
{"level":"debug","message":"Tercero insertado: 4939969","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:56.476Z"}
{"level":"debug","message":"Procesando tercero: 860032909","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:56.476Z"}
{"level":"debug","message":"Tercero insertado: 860032909","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:56.843Z"}
{"level":"debug","message":"Procesando tercero: 31539209","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:56.843Z"}
{"level":"debug","message":"Tercero insertado: 31539209","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:57.220Z"}
{"level":"debug","message":"Procesando tercero: 890306372","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:57.220Z"}
{"level":"debug","message":"Tercero insertado: 890306372","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:57.577Z"}
{"level":"info","message":"Procesando lote 34 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:57.677Z"}
{"level":"debug","message":"Procesando tercero: 89002463","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:57.677Z"}
{"level":"debug","message":"Tercero insertado: 89002463","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:58.303Z"}
{"level":"debug","message":"Procesando tercero: 805013193","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:58.303Z"}
{"level":"debug","message":"Tercero insertado: 805013193","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:58.661Z"}
{"level":"debug","message":"Procesando tercero: 900718396","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:58.661Z"}
{"level":"debug","message":"Tercero insertado: 900718396","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:59.045Z"}
{"level":"debug","message":"Procesando tercero: 900793510","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:59.045Z"}
{"level":"debug","message":"Tercero insertado: 900793510","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:59.395Z"}
{"level":"debug","message":"Procesando tercero: 901548316","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:59.395Z"}
{"level":"debug","message":"Tercero insertado: 901548316","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:59.808Z"}
{"level":"debug","message":"Procesando tercero: 66953488","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:29:59.808Z"}
{"level":"debug","message":"Tercero insertado: 66953488","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:00.157Z"}
{"level":"debug","message":"Procesando tercero: 11165011","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:00.157Z"}
{"level":"debug","message":"Tercero insertado: 11165011","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:00.512Z"}
{"level":"debug","message":"Procesando tercero: 900984567","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:00.512Z"}
{"level":"debug","message":"Tercero insertado: 900984567","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:00.860Z"}
{"level":"debug","message":"Procesando tercero: 6301954","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:00.860Z"}
{"level":"debug","message":"Tercero insertado: 6301954","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:01.234Z"}
{"level":"debug","message":"Procesando tercero: 14994528","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:01.234Z"}
{"level":"debug","message":"Tercero insertado: 14994528","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:01.642Z"}
{"level":"info","message":"Procesando lote 35 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:01.748Z"}
{"level":"debug","message":"Procesando tercero: 901207662","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:01.748Z"}
{"level":"debug","message":"Tercero insertado: 901207662","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:02.111Z"}
{"level":"debug","message":"Procesando tercero: 14679125","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:02.111Z"}
{"level":"debug","message":"Tercero insertado: 14679125","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:02.514Z"}
{"level":"debug","message":"Procesando tercero: 800137960","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:02.514Z"}
{"level":"debug","message":"Tercero insertado: 800137960","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:02.859Z"}
{"level":"debug","message":"Procesando tercero: 900043802","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:02.859Z"}
{"level":"debug","message":"Tercero insertado: 900043802","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:03.217Z"}
{"level":"debug","message":"Procesando tercero: 1005832840","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:03.217Z"}
{"level":"debug","message":"Tercero insertado: 1005832840","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:03.585Z"}
{"level":"debug","message":"Procesando tercero: 1111658389","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:03.585Z"}
{"level":"debug","message":"Tercero insertado: 1111658389","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:03.944Z"}
{"level":"debug","message":"Procesando tercero: 1144093720","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:03.945Z"}
{"level":"debug","message":"Tercero insertado: 1144093720","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:04.289Z"}
{"level":"debug","message":"Procesando tercero: 800251440","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:04.289Z"}
{"level":"debug","message":"Tercero insertado: 800251440","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:04.660Z"}
{"level":"debug","message":"Procesando tercero: 901488897","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:04.660Z"}
{"level":"debug","message":"Tercero insertado: 901488897","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:05.014Z"}
{"level":"debug","message":"Procesando tercero: 10540281","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:05.014Z"}
{"level":"debug","message":"Tercero insertado: 10540281","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:05.396Z"}
{"level":"info","message":"Procesando lote 36 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:05.497Z"}
{"level":"debug","message":"Procesando tercero: 66911497","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:05.497Z"}
{"level":"debug","message":"Tercero insertado: 66911497","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:05.851Z"}
{"level":"debug","message":"Procesando tercero: 815003243","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:05.852Z"}
{"level":"debug","message":"Tercero insertado: 815003243","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:06.231Z"}
{"level":"debug","message":"Procesando tercero: 860000656","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:06.231Z"}
{"level":"debug","message":"Tercero insertado: 860000656","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:06.596Z"}
{"level":"debug","message":"Procesando tercero: 1113066154","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:06.597Z"}
{"level":"debug","message":"Tercero insertado: 1113066154","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:06.969Z"}
{"level":"debug","message":"Procesando tercero: 2550279","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:06.969Z"}
{"level":"debug","message":"Tercero insertado: 2550279","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:07.334Z"}
{"level":"debug","message":"Procesando tercero: 901565965","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:07.334Z"}
{"level":"debug","message":"Tercero insertado: 901565965","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:07.691Z"}
{"level":"debug","message":"Procesando tercero: 94449386","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:07.691Z"}
{"level":"debug","message":"Tercero insertado: 94449386","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:08.042Z"}
{"level":"debug","message":"Procesando tercero: 901389231","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:08.043Z"}
{"level":"debug","message":"Tercero insertado: 901389231","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:08.435Z"}
{"level":"debug","message":"Procesando tercero: 860001584","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:08.435Z"}
{"level":"debug","message":"Tercero insertado: 860001584","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:08.802Z"}
{"level":"debug","message":"Procesando tercero: 9003826786","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:08.802Z"}
{"level":"debug","message":"Tercero insertado: 9003826786","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:09.171Z"}
{"level":"info","message":"Procesando lote 37 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:09.274Z"}
{"level":"debug","message":"Procesando tercero: 901165312","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:09.274Z"}
{"level":"debug","message":"Tercero insertado: 901165312","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:09.633Z"}
{"level":"debug","message":"Procesando tercero: 900319753","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:09.634Z"}
{"level":"debug","message":"Tercero insertado: 900319753","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:10.000Z"}
{"level":"debug","message":"Procesando tercero: 1114338952","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:10.000Z"}
{"level":"debug","message":"Tercero insertado: 1114338952","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:10.375Z"}
{"level":"debug","message":"Procesando tercero: 900134321","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:10.375Z"}
{"level":"debug","message":"Tercero insertado: 900134321","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:10.732Z"}
{"level":"debug","message":"Procesando tercero: 901415702","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:10.732Z"}
{"level":"debug","message":"Tercero insertado: 901415702","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:11.106Z"}
{"level":"debug","message":"Procesando tercero: 800089948","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:11.107Z"}
{"level":"debug","message":"Tercero insertado: 800089948","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:11.481Z"}
{"level":"debug","message":"Procesando tercero: 800175937","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:11.481Z"}
{"level":"debug","message":"Tercero insertado: 800175937","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:11.855Z"}
{"level":"debug","message":"Procesando tercero: 800029899","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:11.855Z"}
{"level":"debug","message":"Tercero insertado: 800029899","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:12.221Z"}
{"level":"debug","message":"Procesando tercero: 1095831460","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:12.221Z"}
{"level":"debug","message":"Tercero insertado: 1095831460","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:12.579Z"}
{"level":"debug","message":"Procesando tercero: 900880839","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:12.579Z"}
{"level":"debug","message":"Tercero insertado: 900880839","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:12.958Z"}
{"level":"info","message":"Procesando lote 38 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:13.059Z"}
{"level":"debug","message":"Procesando tercero: 1144139828","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:13.059Z"}
{"level":"debug","message":"Tercero insertado: 1144139828","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:13.441Z"}
{"level":"debug","message":"Procesando tercero: 94452846","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:13.442Z"}
{"level":"debug","message":"Tercero insertado: 94452846","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:13.831Z"}
{"level":"debug","message":"Procesando tercero: 900608991","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:13.831Z"}
{"level":"debug","message":"Tercero insertado: 900608991","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:14.233Z"}
{"level":"debug","message":"Procesando tercero: 70694641","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:14.233Z"}
{"level":"debug","message":"Tercero insertado: 70694641","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:14.582Z"}
{"level":"debug","message":"Procesando tercero: 900838855","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:14.582Z"}
{"level":"debug","message":"Tercero insertado: 900838855","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:14.936Z"}
{"level":"debug","message":"Procesando tercero: 1088252373","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:14.936Z"}
{"level":"debug","message":"Tercero insertado: 1088252373","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:15.328Z"}
{"level":"debug","message":"Procesando tercero: 15914993","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:15.328Z"}
{"level":"debug","message":"Tercero insertado: 15914993","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:15.688Z"}
{"level":"debug","message":"Procesando tercero: 805027305","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:15.689Z"}
{"level":"debug","message":"Tercero insertado: 805027305","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:16.051Z"}
{"level":"debug","message":"Procesando tercero: 22082157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:16.051Z"}
{"level":"debug","message":"Tercero insertado: 22082157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:16.421Z"}
{"level":"debug","message":"Procesando tercero: 31956962","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:16.422Z"}
{"level":"debug","message":"Tercero insertado: 31956962","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:16.791Z"}
{"level":"info","message":"Procesando lote 39 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:16.892Z"}
{"level":"debug","message":"Procesando tercero: 809010690","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:16.893Z"}
{"level":"debug","message":"Tercero insertado: 809010690","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:17.247Z"}
{"level":"debug","message":"Procesando tercero: 94417613","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:17.247Z"}
{"level":"debug","message":"Tercero insertado: 94417613","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:17.601Z"}
{"level":"debug","message":"Procesando tercero: 830081407","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:17.602Z"}
{"level":"debug","message":"Tercero insertado: 830081407","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:18.044Z"}
{"level":"debug","message":"Procesando tercero: 901562758","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:18.044Z"}
{"level":"debug","message":"Tercero insertado: 901562758","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:18.399Z"}
{"level":"debug","message":"Procesando tercero: 900143655","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:18.399Z"}
{"level":"debug","message":"Tercero insertado: 900143655","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:18.737Z"}
{"level":"debug","message":"Procesando tercero: 901621257","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:18.737Z"}
{"level":"debug","message":"Tercero insertado: 901621257","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:19.108Z"}
{"level":"debug","message":"Procesando tercero: 900553985","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:19.108Z"}
{"level":"debug","message":"Tercero insertado: 900553985","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:19.482Z"}
{"level":"debug","message":"Procesando tercero: 800186960","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:19.483Z"}
{"level":"debug","message":"Tercero insertado: 800186960","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:19.852Z"}
{"level":"debug","message":"Procesando tercero: 1143833936","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:19.852Z"}
{"level":"debug","message":"Tercero insertado: 1143833936","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:20.226Z"}
{"level":"debug","message":"Procesando tercero: 1038416432","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:20.227Z"}
{"level":"debug","message":"Tercero insertado: 1038416432","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:20.585Z"}
{"level":"info","message":"Procesando lote 40 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:20.700Z"}
{"level":"debug","message":"Procesando tercero: 811025446","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:20.700Z"}
{"level":"debug","message":"Tercero insertado: 811025446","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:21.059Z"}
{"level":"debug","message":"Procesando tercero: 901026549","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:21.059Z"}
{"level":"debug","message":"Tercero insertado: 901026549","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:21.417Z"}
{"level":"debug","message":"Procesando tercero: 18143852","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:21.417Z"}
{"level":"debug","message":"Tercero insertado: 18143852","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:21.757Z"}
{"level":"debug","message":"Procesando tercero: 7038370383990","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:21.758Z"}
{"level":"debug","message":"Tercero insertado: 7038370383990","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:22.126Z"}
{"level":"debug","message":"Procesando tercero: 1114736118","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:22.127Z"}
{"level":"debug","message":"Tercero insertado: 1114736118","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:22.473Z"}
{"level":"debug","message":"Procesando tercero: 860060431","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:22.474Z"}
{"level":"debug","message":"Tercero insertado: 860060431","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:22.835Z"}
{"level":"debug","message":"Procesando tercero: 900480569","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:22.835Z"}
{"level":"debug","message":"Tercero insertado: 900480569","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:23.212Z"}
{"level":"debug","message":"Procesando tercero: 1144129245","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:23.213Z"}
{"level":"debug","message":"Tercero insertado: 1144129245","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:23.585Z"}
{"level":"debug","message":"Procesando tercero: 1112476450","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:23.585Z"}
{"level":"debug","message":"Tercero insertado: 1112476450","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:23.944Z"}
{"level":"debug","message":"Procesando tercero: 1007192774","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:23.945Z"}
{"level":"debug","message":"Tercero insertado: 1007192774","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:24.305Z"}
{"level":"info","message":"Procesando lote 41 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:24.413Z"}
{"level":"debug","message":"Procesando tercero: 900392722","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:24.413Z"}
{"level":"debug","message":"Tercero insertado: 900392722","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:24.767Z"}
{"level":"debug","message":"Procesando tercero: 890323635","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:24.768Z"}
{"level":"debug","message":"Tercero insertado: 890323635","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:25.132Z"}
{"level":"debug","message":"Procesando tercero: 901428953","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:25.132Z"}
{"level":"debug","message":"Tercero insertado: 901428953","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:25.498Z"}
{"level":"debug","message":"Procesando tercero: 805029104","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:25.498Z"}
{"level":"debug","message":"Tercero insertado: 805029104","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:25.881Z"}
{"level":"debug","message":"Procesando tercero: 900212550","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:25.881Z"}
{"level":"debug","message":"Tercero insertado: 900212550","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:26.241Z"}
{"level":"debug","message":"Procesando tercero: 6106213","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:26.241Z"}
{"level":"debug","message":"Tercero insertado: 6106213","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:26.626Z"}
{"level":"debug","message":"Procesando tercero: 1114734010","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:26.626Z"}
{"level":"debug","message":"Tercero insertado: 1114734010","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:26.992Z"}
{"level":"debug","message":"Procesando tercero: 6246720","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:26.992Z"}
{"level":"debug","message":"Tercero insertado: 6246720","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:27.379Z"}
{"level":"debug","message":"Procesando tercero: 901694809","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:27.379Z"}
{"level":"debug","message":"Tercero insertado: 901694809","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:27.750Z"}
{"level":"debug","message":"Procesando tercero: 14621503","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:27.750Z"}
{"level":"debug","message":"Tercero insertado: 14621503","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:28.137Z"}
{"level":"info","message":"Procesando lote 42 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:28.239Z"}
{"level":"debug","message":"Procesando tercero: 29422224","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:28.239Z"}
{"level":"debug","message":"Tercero insertado: 29422224","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:28.585Z"}
{"level":"debug","message":"Procesando tercero: 900206480","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:28.585Z"}
{"level":"debug","message":"Tercero insertado: 900206480","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:28.948Z"}
{"level":"debug","message":"Procesando tercero: 901698553","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:28.949Z"}
{"level":"debug","message":"Tercero insertado: 901698553","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:29.310Z"}
{"level":"debug","message":"Procesando tercero: 1114730283","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:29.310Z"}
{"level":"debug","message":"Tercero insertado: 1114730283","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:29.665Z"}
{"level":"debug","message":"Procesando tercero: 10473658","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:29.665Z"}
{"level":"debug","message":"Tercero insertado: 10473658","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:30.035Z"}
{"level":"debug","message":"Procesando tercero: 901715368","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:30.035Z"}
{"level":"debug","message":"Tercero insertado: 901715368","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:30.416Z"}
{"level":"debug","message":"Procesando tercero: 900982906","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:30.416Z"}
{"level":"debug","message":"Tercero insertado: 900982906","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:30.785Z"}
{"level":"debug","message":"Procesando tercero: 805026175","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:30.785Z"}
{"level":"debug","message":"Tercero insertado: 805026175","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:31.185Z"}
{"level":"debug","message":"Procesando tercero: 830512753","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:31.186Z"}
{"level":"debug","message":"Tercero insertado: 830512753","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:31.616Z"}
{"level":"debug","message":"Procesando tercero: 830111367","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:31.616Z"}
{"level":"debug","message":"Tercero insertado: 830111367","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:31.993Z"}
{"level":"info","message":"Procesando lote 43 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:32.095Z"}
{"level":"debug","message":"Procesando tercero: 809007911","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:32.095Z"}
{"level":"debug","message":"Tercero insertado: 809007911","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:32.521Z"}
{"level":"debug","message":"Procesando tercero: 805012966","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:32.521Z"}
{"level":"debug","message":"Tercero insertado: 805012966","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:32.948Z"}
{"level":"debug","message":"Procesando tercero: 10592858","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:32.949Z"}
{"level":"debug","message":"Tercero insertado: 10592858","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:33.318Z"}
{"level":"debug","message":"Procesando tercero: 890301686","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:33.318Z"}
{"level":"debug","message":"Tercero insertado: 890301686","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:33.688Z"}
{"level":"debug","message":"Procesando tercero: 901690831","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:33.689Z"}
{"level":"debug","message":"Tercero insertado: 901690831","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:34.339Z"}
{"level":"debug","message":"Procesando tercero: 900474144","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:34.339Z"}
{"level":"debug","message":"Tercero insertado: 900474144","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:34.704Z"}
{"level":"debug","message":"Procesando tercero: 1114390350","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:34.705Z"}
{"level":"debug","message":"Tercero insertado: 1114390350","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:35.088Z"}
{"level":"debug","message":"Procesando tercero: 29400235","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:35.089Z"}
{"level":"debug","message":"Tercero insertado: 29400235","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:35.476Z"}
{"level":"debug","message":"Procesando tercero: 805003603","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:35.476Z"}
{"level":"debug","message":"Tercero insertado: 805003603","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:35.870Z"}
{"level":"debug","message":"Procesando tercero: 800040182","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:35.870Z"}
{"level":"debug","message":"Tercero insertado: 800040182","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:36.216Z"}
{"level":"info","message":"Procesando lote 44 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:36.316Z"}
{"level":"debug","message":"Procesando tercero: 14697776","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:36.316Z"}
{"level":"debug","message":"Tercero insertado: 14697776","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:36.728Z"}
{"level":"debug","message":"Procesando tercero: 901400566","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:36.728Z"}
{"level":"debug","message":"Tercero insertado: 901400566","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:37.168Z"}
{"level":"debug","message":"Procesando tercero: 901635858","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:37.168Z"}
{"level":"debug","message":"Tercero insertado: 901635858","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:37.556Z"}
{"level":"debug","message":"Procesando tercero: 805015618","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:37.556Z"}
{"level":"debug","message":"Tercero insertado: 805015618","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:37.930Z"}
{"level":"debug","message":"Procesando tercero: 891303109","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:37.930Z"}
{"level":"debug","message":"Tercero insertado: 891303109","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:38.292Z"}
{"level":"debug","message":"Procesando tercero: 901758354","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:38.292Z"}
{"level":"debug","message":"Tercero insertado: 901758354","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:38.677Z"}
{"level":"debug","message":"Procesando tercero: 901556230","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:38.677Z"}
{"level":"debug","message":"Tercero insertado: 901556230","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:39.060Z"}
{"level":"debug","message":"Procesando tercero: 900397836","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:39.060Z"}
{"level":"debug","message":"Tercero insertado: 900397836","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:39.434Z"}
{"level":"debug","message":"Procesando tercero: 1107043357","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:39.434Z"}
{"level":"debug","message":"Tercero insertado: 1107043357","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:39.812Z"}
{"level":"debug","message":"Procesando tercero: 901004878","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:39.812Z"}
{"level":"debug","message":"Tercero insertado: 901004878","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:40.200Z"}
{"level":"info","message":"Procesando lote 45 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:40.303Z"}
{"level":"debug","message":"Procesando tercero: 94419662","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:40.303Z"}
{"level":"debug","message":"Tercero insertado: 94419662","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:40.658Z"}
{"level":"debug","message":"Procesando tercero: 891300382","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:40.659Z"}
{"level":"debug","message":"Tercero insertado: 891300382","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:41.022Z"}
{"level":"debug","message":"Procesando tercero: 890305174","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:41.023Z"}
{"level":"debug","message":"Tercero insertado: 890305174","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:41.410Z"}
{"level":"debug","message":"Procesando tercero: 900981115","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:41.410Z"}
{"level":"debug","message":"Tercero insertado: 900981115","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:41.791Z"}
{"level":"debug","message":"Procesando tercero: 9009432434","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:41.791Z"}
{"level":"debug","message":"Tercero insertado: 9009432434","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:42.172Z"}
{"level":"debug","message":"Procesando tercero: 900813135","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:42.172Z"}
{"level":"debug","message":"Tercero insertado: 900813135","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:42.538Z"}
{"level":"debug","message":"Procesando tercero: 14565656","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:42.538Z"}
{"level":"debug","message":"Tercero insertado: 14565656","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:42.930Z"}
{"level":"debug","message":"Procesando tercero: 1234198093","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:42.930Z"}
{"level":"debug","message":"Tercero insertado: 1234198093","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:43.284Z"}
{"level":"debug","message":"Procesando tercero: 1012426129","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:43.285Z"}
{"level":"debug","message":"Tercero insertado: 1012426129","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:43.649Z"}
{"level":"debug","message":"Procesando tercero: 1030556350","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:43.649Z"}
{"level":"debug","message":"Tercero insertado: 1030556350","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:44.013Z"}
{"level":"info","message":"Procesando lote 46 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:44.116Z"}
{"level":"debug","message":"Procesando tercero: 901257828","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:44.116Z"}
{"level":"debug","message":"Tercero insertado: 901257828","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:44.480Z"}
{"level":"debug","message":"Procesando tercero: 94072753","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:44.480Z"}
{"level":"debug","message":"Tercero insertado: 94072753","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:44.835Z"}
{"level":"debug","message":"Procesando tercero: 222222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:44.836Z"}
{"level":"debug","message":"Tercero insertado: 222222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:45.202Z"}
{"level":"debug","message":"Procesando tercero: 21","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:45.202Z"}
{"level":"debug","message":"Tercero insertado: 21","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:45.578Z"}
{"level":"debug","message":"Procesando tercero: 66887824","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:45.578Z"}
{"level":"debug","message":"Tercero insertado: 66887824","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:45.943Z"}
{"level":"debug","message":"Procesando tercero: 1114735235","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:45.943Z"}
{"level":"debug","message":"Tercero insertado: 1114735235","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:46.327Z"}
{"level":"debug","message":"Procesando tercero: 805017536","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:46.327Z"}
{"level":"debug","message":"Tercero insertado: 805017536","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:46.681Z"}
{"level":"debug","message":"Procesando tercero: 1111739602","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:46.681Z"}
{"level":"debug","message":"Tercero insertado: 1111739602","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:47.055Z"}
{"level":"debug","message":"Procesando tercero: 901821217","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:47.056Z"}
{"level":"debug","message":"Tercero insertado: 901821217","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:47.420Z"}
{"level":"debug","message":"Procesando tercero: 805031628","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:47.420Z"}
{"level":"debug","message":"Tercero insertado: 805031628","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:47.792Z"}
{"level":"info","message":"Procesando lote 47 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:47.900Z"}
{"level":"debug","message":"Procesando tercero: 805021170","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:47.901Z"}
{"level":"debug","message":"Tercero insertado: 805021170","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:48.257Z"}
{"level":"debug","message":"Procesando tercero: 16942359","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:48.258Z"}
{"level":"debug","message":"Tercero insertado: 16942359","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:48.648Z"}
{"level":"debug","message":"Procesando tercero: 901089418","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:48.648Z"}
{"level":"debug","message":"Tercero insertado: 901089418","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:49.017Z"}
{"level":"debug","message":"Procesando tercero: 94404007","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:49.017Z"}
{"level":"debug","message":"Tercero insertado: 94404007","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:49.369Z"}
{"level":"debug","message":"Procesando tercero: 890321760","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:49.369Z"}
{"level":"debug","message":"Tercero insertado: 890321760","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:49.735Z"}
{"level":"debug","message":"Procesando tercero: 94422226","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:49.735Z"}
{"level":"debug","message":"Tercero insertado: 94422226","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:50.094Z"}
{"level":"debug","message":"Procesando tercero: 901753208","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:50.094Z"}
{"level":"debug","message":"Tercero insertado: 901753208","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:50.456Z"}
{"level":"debug","message":"Procesando tercero: 901308671","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:50.456Z"}
{"level":"debug","message":"Tercero insertado: 901308671","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:50.824Z"}
{"level":"debug","message":"Procesando tercero: 805002619","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:50.825Z"}
{"level":"debug","message":"Tercero insertado: 805002619","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:51.179Z"}
{"level":"debug","message":"Procesando tercero: 9869318","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:51.180Z"}
{"level":"debug","message":"Tercero insertado: 9869318","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:51.552Z"}
{"level":"info","message":"Procesando lote 48 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:51.664Z"}
{"level":"debug","message":"Procesando tercero: 830082693","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:51.664Z"}
{"level":"debug","message":"Tercero insertado: 830082693","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:52.022Z"}
{"level":"debug","message":"Procesando tercero: 901585893","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:52.022Z"}
{"level":"debug","message":"Tercero insertado: 901585893","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:52.390Z"}
{"level":"debug","message":"Procesando tercero: 222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:52.391Z"}
{"level":"debug","message":"Tercero insertado: 222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:52.779Z"}
{"level":"debug","message":"Procesando tercero: 444444000","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:52.779Z"}
{"level":"debug","message":"Tercero insertado: 444444000","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:53.154Z"}
{"level":"debug","message":"Procesando tercero: 900598777","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:53.154Z"}
{"level":"debug","message":"Tercero insertado: 900598777","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:53.525Z"}
{"level":"debug","message":"Procesando tercero: 901078623","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:53.525Z"}
{"level":"debug","message":"Tercero insertado: 901078623","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:53.907Z"}
{"level":"debug","message":"Procesando tercero: 900537807","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:53.907Z"}
{"level":"debug","message":"Tercero insertado: 900537807","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:54.299Z"}
{"level":"debug","message":"Procesando tercero: 900387508","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:54.299Z"}
{"level":"debug","message":"Tercero insertado: 900387508","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:54.662Z"}
{"level":"debug","message":"Procesando tercero: 31380094","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:54.662Z"}
{"level":"debug","message":"Tercero insertado: 31380094","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:55.040Z"}
{"level":"debug","message":"Procesando tercero: 890201881","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:55.041Z"}
{"level":"debug","message":"Tercero insertado: 890201881","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:55.400Z"}
{"level":"info","message":"Procesando lote 49 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:55.510Z"}
{"level":"debug","message":"Procesando tercero: 900256360","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:55.510Z"}
{"level":"debug","message":"Tercero insertado: 900256360","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:55.869Z"}
{"level":"debug","message":"Procesando tercero: 900211969","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:55.869Z"}
{"level":"debug","message":"Tercero insertado: 900211969","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:56.227Z"}
{"level":"debug","message":"Procesando tercero: 16763663","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:56.227Z"}
{"level":"debug","message":"Tercero insertado: 16763663","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:56.589Z"}
{"level":"debug","message":"Procesando tercero: 860534900","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:56.590Z"}
{"level":"debug","message":"Tercero insertado: 860534900","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:56.952Z"}
{"level":"debug","message":"Procesando tercero: 901407000","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:56.952Z"}
{"level":"debug","message":"Tercero insertado: 901407000","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:57.318Z"}
{"level":"debug","message":"Procesando tercero: 38866426","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:57.318Z"}
{"level":"debug","message":"Tercero insertado: 38866426","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:57.678Z"}
{"level":"debug","message":"Procesando tercero: 14575814","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:57.678Z"}
{"level":"debug","message":"Tercero insertado: 14575814","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:58.040Z"}
{"level":"debug","message":"Procesando tercero: 900609431","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:58.040Z"}
{"level":"debug","message":"Tercero insertado: 900609431","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:58.410Z"}
{"level":"debug","message":"Procesando tercero: 67008667","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:58.410Z"}
{"level":"debug","message":"Tercero insertado: 67008667","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:58.777Z"}
{"level":"debug","message":"Procesando tercero: 1193225121","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:58.777Z"}
{"level":"debug","message":"Tercero insertado: 1193225121","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:59.156Z"}
{"level":"info","message":"Procesando lote 50 de 50","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:59.271Z"}
{"level":"debug","message":"Procesando tercero: 800140016","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:59.271Z"}
{"level":"debug","message":"Tercero insertado: 800140016","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:59.642Z"}
{"level":"debug","message":"Procesando tercero: 901319946","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:30:59.642Z"}
{"level":"debug","message":"Tercero insertado: 901319946","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:00.011Z"}
{"level":"debug","message":"Procesando tercero: 31642365","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:00.011Z"}
{"level":"debug","message":"Tercero insertado: 31642365","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:00.398Z"}
{"level":"debug","message":"Procesando tercero: 900252727","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:00.398Z"}
{"level":"debug","message":"Tercero insertado: 900252727","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:00.757Z"}
{"level":"debug","message":"Procesando tercero: 890305711","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:00.757Z"}
{"level":"debug","message":"Tercero insertado: 890305711","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:01.129Z"}
{"level":"debug","message":"Procesando tercero: 16778509","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:01.129Z"}
{"level":"debug","message":"Tercero insertado: 16778509","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:01.499Z"}
{"level":"debug","message":"Procesando tercero: 1053836282","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:01.499Z"}
{"level":"debug","message":"Tercero insertado: 1053836282","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:01.874Z"}
{"level":"info","message":"Sincronización completada: 497 procesados, 0 errores","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:01.874Z"}
{"level":"info","message":"Sincronización de terceros completada: 497 procesados, 0 errores","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:01.874Z"}
{"level":"info","message":"Iniciando sincronización automática de cuentas contables...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:31.876Z"}
{"level":"info","message":"Iniciando sincronización de cuentas contables...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:31.876Z"}
{"level":"info","message":"Iniciando sincronización de cuentas contables (completa: false)","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:31.876Z"}
{"level":"info","message":"Encontradas 1 cuentas para sincronizar","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:31.881Z"}
{"level":"info","message":"Procesando lote 1 de 1","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:31.881Z"}
{"level":"debug","message":"Procesando cuenta: 11050501","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:31.882Z"}
{"level":"debug","message":"Cuenta insertada: 11050501","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:32.430Z"}
{"level":"info","message":"Sincronización de cuentas completada: 1 procesadas, 0 errores","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:32.430Z"}
{"level":"info","message":"Sincronización de cuentas completada: 1 procesadas, 0 errores","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:31:32.431Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:32:11.393Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.298Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.301Z"}
{"level":"info","message":"Cliente Supabase inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.302Z"}
{"level":"info","message":"Iniciando servicio de sincronización Supabase-Firebird...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.302Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.340Z"}
{"level":"info","message":"Tipo FIA ya existe en TIPDOC","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.345Z"}
{"level":"info","message":"Servicio de sincronización inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.346Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.357Z"}
{"level":"info","message":"Servicio de sincronización de terceros inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.358Z"}
{"level":"info","message":"Conexión a Firebird establecida exitosamente","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.369Z"}
{"level":"info","message":"Servicio de sincronización de cuentas contables inicializado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.369Z"}
{"accountRanges":[{"end":********,"start":********},{"end":********,"start":********},{"end":********,"start":********},{"end":********,"start":********},{"end":********,"start":********}],"excludeZeroLevel":true,"level":"info","message":"Configuración de sincronización:","onlyActiveAccounts":true,"service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.370Z"}
{"level":"info","message":"Listener de Supabase Realtime configurado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.402Z"}
{"level":"info","message":"Sincronizaciones en segundo plano configuradas","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.402Z"}
{"level":"info","message":"Servicio de sincronización iniciado y escuchando cambios...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.402Z"}
{"level":"info","message":"Sincronización de terceros programada cada 30 minutos","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.402Z"}
{"level":"info","message":"Sincronización de cuentas programada cada 120 minutos","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.403Z"}
{"level":"info","message":"Servicio iniciado exitosamente. Presiona Ctrl+C para detener.","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.406Z"}
{"level":"info","message":"API de control disponible en http://localhost:3001","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.407Z"}
{"level":"info","message":"Endpoints disponibles:","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.407Z"}
{"level":"info","message":"  GET  /health - Estado del servicio","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.407Z"}
{"level":"info","message":"  GET  /api/sync/status - Estado de sincronización","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.407Z"}
{"level":"info","message":"  POST /api/sync/third-parties - Sincronización manual de terceros","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.408Z"}
{"level":"info","message":"  GET  /api/sync/third-parties/stats - Estadísticas de terceros","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.408Z"}
{"level":"info","message":"  POST /api/sync/chart-of-accounts - Sincronización manual de cuentas","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.408Z"}
{"level":"info","message":"  GET  /api/sync/chart-of-accounts/stats - Estadísticas de cuentas","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.408Z"}
{"level":"info","message":"  GET  /api/sync/chart-of-accounts/config - Configuración de cuentas","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:36:24.408Z"}
{"level":"info","message":"Ejecutando sincronizaciones iniciales...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:24.404Z"}
{"level":"info","message":"Iniciando sincronización automática de terceros...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:24.405Z"}
{"level":"info","message":"Iniciando sincronización incremental de terceros...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:24.405Z"}
{"level":"info","message":"Iniciando sincronización de terceros (fullSync: false)","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:24.406Z"}
{"level":"info","message":"Última versión sincronizada: 653","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:24.646Z"}
{"level":"info","message":"Encontrados 23 terceros para sincronizar","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:24.654Z"}
{"level":"info","message":"Procesando lote 1 de 3","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:24.654Z"}
{"level":"debug","message":"Procesando tercero: 800231967","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:24.655Z"}
{"level":"debug","message":"Tercero actualizado: 800231967","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:25.004Z"}
{"level":"debug","message":"Procesando tercero: 1","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:25.004Z"}
{"level":"debug","message":"Tercero actualizado: 1","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:25.398Z"}
{"level":"debug","message":"Procesando tercero: 800229739","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:25.398Z"}
{"level":"debug","message":"Tercero actualizado: 800229739","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:25.751Z"}
{"level":"debug","message":"Procesando tercero: 999999","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:25.752Z"}
{"level":"debug","message":"Tercero actualizado: 999999","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:26.087Z"}
{"level":"debug","message":"Procesando tercero: 800224827","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:26.087Z"}
{"level":"debug","message":"Tercero actualizado: 800224827","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:26.432Z"}
{"level":"debug","message":"Procesando tercero: 805001157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:26.433Z"}
{"level":"debug","message":"Tercero actualizado: 805001157","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:26.775Z"}
{"level":"debug","message":"Procesando tercero: 800088702","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:26.776Z"}
{"level":"debug","message":"Tercero actualizado: 800088702","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:27.130Z"}
{"level":"debug","message":"Procesando tercero: 805000427","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:27.131Z"}
{"level":"debug","message":"Tercero actualizado: 805000427","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:27.477Z"}
{"level":"debug","message":"Procesando tercero: 800144331","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:27.477Z"}
{"level":"debug","message":"Tercero actualizado: 800144331","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:27.810Z"}
{"level":"debug","message":"Procesando tercero: 800140949","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:27.810Z"}
{"level":"debug","message":"Tercero actualizado: 800140949","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:28.241Z"}
{"level":"info","message":"Procesando lote 2 de 3","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:28.343Z"}
{"level":"debug","message":"Procesando tercero: 860007336","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:28.344Z"}
{"level":"debug","message":"Tercero actualizado: 860007336","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:28.714Z"}
{"level":"debug","message":"Procesando tercero: 830009783","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:28.714Z"}
{"level":"debug","message":"Tercero actualizado: 830009783","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:29.049Z"}
{"level":"debug","message":"Procesando tercero: 830113831","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:29.049Z"}
{"level":"debug","message":"Tercero actualizado: 830113831","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:29.394Z"}
{"level":"debug","message":"Procesando tercero: 800256161","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:29.394Z"}
{"level":"debug","message":"Tercero actualizado: 800256161","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:29.720Z"}
{"level":"debug","message":"Procesando tercero: 830003564","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:29.720Z"}
{"level":"debug","message":"Tercero actualizado: 830003564","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:30.043Z"}
{"level":"debug","message":"Procesando tercero: 899999239","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:30.043Z"}
{"level":"debug","message":"Tercero actualizado: 899999239","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:30.355Z"}
{"level":"debug","message":"Procesando tercero: 800227940","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:30.355Z"}
{"level":"debug","message":"Tercero actualizado: 800227940","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:30.681Z"}
{"level":"debug","message":"Procesando tercero: 890303208","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:30.681Z"}
{"level":"debug","message":"Tercero actualizado: 890303208","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:31.021Z"}
{"level":"debug","message":"Procesando tercero: 890303093","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:31.021Z"}
{"level":"debug","message":"Tercero actualizado: 890303093","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:31.356Z"}
{"level":"debug","message":"Procesando tercero: 800226175","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:31.356Z"}
{"level":"debug","message":"Tercero actualizado: 800226175","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:31.686Z"}
{"level":"info","message":"Procesando lote 3 de 3","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:31.788Z"}
{"level":"debug","message":"Procesando tercero: 800130907","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:31.789Z"}
{"level":"debug","message":"Tercero actualizado: 800130907","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:32.125Z"}
{"level":"debug","message":"Procesando tercero: 899999034","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:32.125Z"}
{"level":"debug","message":"Tercero actualizado: 899999034","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:32.444Z"}
{"level":"debug","message":"Procesando tercero: 22222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:32.445Z"}
{"level":"debug","message":"Tercero actualizado: 22222222","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:32.810Z"}
{"level":"info","message":"Sincronización completada: 23 procesados, 0 errores","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:32.811Z"}
{"level":"info","message":"Sincronización de terceros completada: 23 procesados, 0 errores","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:37:32.811Z"}
{"level":"info","message":"Iniciando sincronización automática de cuentas contables...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:38:02.816Z"}
{"level":"info","message":"Iniciando sincronización de cuentas contables...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:38:02.816Z"}
{"level":"info","message":"Iniciando sincronización de cuentas contables (completa: false)","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:38:02.817Z"}
{"level":"info","message":"Encontradas 1 cuentas para sincronizar","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:38:02.820Z"}
{"level":"info","message":"Procesando lote 1 de 1","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:38:02.820Z"}
{"level":"debug","message":"Procesando cuenta: 11050501","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:38:02.821Z"}
{"level":"debug","message":"Cuenta actualizada: 11050501","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:38:03.359Z"}
{"level":"info","message":"Sincronización de cuentas completada: 1 procesadas, 0 errores","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:38:03.360Z"}
{"level":"info","message":"Sincronización de cuentas completada: 1 procesadas, 0 errores","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:38:03.360Z"}
{"level":"info","message":"Recibida señal SIGINT, cerrando servicio...","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:41:35.069Z"}
{"level":"info","message":"Intervalo de sincronización de terceros detenido","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:41:35.070Z"}
{"level":"info","message":"Intervalo de sincronización de cuentas detenido","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:41:35.070Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:41:35.071Z"}
{"level":"info","message":"Cliente Supabase cerrado","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:41:35.072Z"}
{"level":"info","message":"Conexión Firebird cerrada","service":"supabase-firebird-sync","timestamp":"2025-06-20T19:41:35.072Z"}
