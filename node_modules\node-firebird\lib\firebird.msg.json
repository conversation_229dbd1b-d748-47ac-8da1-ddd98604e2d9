{"335544321": "Arithmetic exception, numeric overflow, or string truncation", "335544322": "Invalid database key", "335544323": "File @1 is not a valid database", "335544324": "Invalid database handle (no active connection)", "335544325": "Bad parameters on attach or create database", "335544326": "Unrecognized database parameter block", "335544327": "Invalid request handle", "335544328": "Invalid BLOB handle", "335544329": "Invalid BLOB ID", "335544330": "Invalid parameter in transaction parameter block", "335544331": "Invalid format for transaction parameter block", "335544332": "Invalid transaction handle (expecting explicit transaction start)", "335544333": "Internal gds software consistency check (@1)", "335544334": "Conversion error from string \"@1\"", "335544335": "Database file appears corrupt (@1)", "335544336": "Deadlock", "335544337": "Attempt to start more than @1 transactions", "335544338": "No match for first value expression", "335544339": "Information type inappropriate for object specified", "335544340": "No information of this type available for object specified", "335544341": "Unknown information item", "335544342": "Action cancelled by trigger (@1) to preserve data integrity", "335544343": "Invalid request BLR at offset @1", "335544344": "I/O error for file \"@2\"", "335544345": "Lock conflict on no wait transaction", "335544346": "Corrupt system table", "335544347": "Validation error for column @1, value \"@2\"", "335544348": "No current record for fetch operation", "335544349": "Attempt to store duplicate value ( visible to active transactions ) in unique index \"@1\"", "335544350": "Program attempted to exit without finishing database", "335544351": "Unsuccessful metadata update", "335544352": "No permission for @1 access to @2 @3", "335544353": "Transaction is not in limbo", "335544354": "Invalid database key", "335544355": "BLOB was not closed", "335544356": "Metadata is obsolete", "335544357": "Cannot disconnect database with open transactions (@1 active)", "335544358": "Message length error ( encountered @1, expected @2)", "335544359": "Attempted update of read - only column", "335544360": "Attempted update of read-only table", "335544361": "Attempted update during read - only transaction", "335544362": "Cannot update read-only view @1", "335544363": "No transaction for request", "335544364": "Request synchronization error", "335544365": "Request referenced an unavailable database", "335544366": "Segment buffer length shorter than expected", "335544367": "Attempted retrieval of more segments than exist", "335544368": "Attempted invalid operation on a BLOB", "335544369": "Attempted read of a new, open BLOB", "335544370": "Attempted action on blob outside transaction", "335544371": "Attempted write to read-only BLOB", "335544372": "Attempted reference to BLOB in unavailable database", "335544373": "Operating system directive @1 failed", "335544374": "Attempt to fetch past the last record in a record stream", "335544375": "Unavailable database", "335544376": "Table @1 was omitted from the transaction reserving list", "335544377": "Request includes a DSRI extension not supported in this implementation", "335544378": "Feature is not supported", "*********": "Unsupported on - disk structure for file @1; found @2.@3, support @4.@5", "*********": "Wrong number of arguments on call", "*********": "Implementation limit exceeded", "*********": "@1", "*********": "Unrecoverable conflict with limbo transaction @1", "*********": "Internal error", "*********": "Internal error", "*********": "Too many requests", "*********": "Internal error", "*********": "Block size exceeds implementation restriction", "*********": "Buffer exhausted", "*********": "BLR syntax error: expected @1 at offset @2, encountered @3", "*********": "Buffer in use", "*********": "Internal error", "*********": "Request in use", "*********": "Incompatible version of on-disk structure", "*********": "Table @1 is not defined", "*********": "Column @1 is not defined in table @2", "*********": "Internal error", "*********": "Internal error", "*********": "Internal error", "*********": "Internal error", "*********": "Internal error", "*********": "Internal error", "*********": "Page @1 is of wrong type (expected @2, found @3)", "*********": "Database corrupted", "*********": "Checksum error on database page @1", "*********": "Index is broken", "*********": "Database handle not zero", "*********": "Transaction handle not zero", "*********": "Transaction - request mismatch ( synchronization error )", "*********": "Bad handle count", "*********": "Wrong version of transaction parameter block", "*********": "Unsupported BLR version (expected @1, encountered @2)", "*********": "Wrong version of database parameter block", "335544414": "BLOB and array data types are not supported for @1 operation", "335544415": "Database corrupted", "335544416": "Internal error", "335544417": "Internal error", "335544418": "Transaction in limbo", "335544419": "Transaction not in limbo", "335544420": "Transaction outstanding", "335544421": "Connection rejected by remote interface", "335544422": "Internal error", "335544423": "Internal error", "335544424": "No lock manager available", "335544425": "Context already in use (BLR error)", "335544426": "Context not defined (BLR error)", "335544427": "Data operation not supported", "335544428": "Undefined message number", "335544429": "Bad parameter number", "335544430": "Unable to allocate memory from operating system", "335544431": "Blocking signal has been received", "335544432": "Lock manager error", "335544433": "communication error with journal \"@1\"", "335544434": "Key size exceeds implementation restriction for index \"@1\"", "335544435": "Null segment of UNIQUE KEY", "335544436": "SQL error code = @1", "335544437": "Wrong DYN version", "335544438": "Function @1 is not defined", "335544439": "Function @1 could not be matched", "335544440": "-", "335544441": "Database detach completed with errors", "335544442": "Database system cannot read argument @1", "335544443": "Database system cannot write argument @1", "335544444": "Operation not supported", "335544445": "@1 extension error", "335544446": "Not updatable", "335544447": "No rollback performed", "335544448": "[no associated message]", "335544449": "[no associated message]", "335544450": "@1", "335544451": "Update conflicts with concurrent update", "335544452": "product @1 is not licensed", "335544453": "Object @1 is in use", "335544454": "Filter not found to convert type @1 to type @2", "335544455": "Cannot attach active shadow file", "335544456": "Invalid slice description language at offset @1", "335544457": "Subscript out of bounds", "335544458": "Column not array or invalid dimensions (expected @1, encountered @2)", "335544459": "Record from transaction @1 is stuck in limbo", "335544460": "A file in manual shadow @1 is unavailable", "335544461": "Secondary server attachments cannot validate databases", "335544462": "secondary server attachments cannot start journaling", "335544463": "Generator @1 is not defined", "335544464": "Secondary server attachments cannot start logging", "335544465": "Invalid BLOB type for operation", "335544466": "Violation of FOREIGN KEY constraint \"@1\" on table \"@2\"", "335544467": "Minor version too high found @1 expected @2", "335544468": "Transaction @1 is @2", "335544469": "Transaction marked invalid by I/O error", "335544470": "Cache buffer for page @1 invalid", "335544471": "There is no index in table @1 with id @2", "335544472": "Your user name and password are not defined. Ask your database\nadministrator to set up a Firebird login\n", "335544473": "Invalid bookmark handle", "335544474": "Invalid lock level @1", "335544475": "Lock on table @1 conflicts with existing lock", "335544476": "Requested record lock conflicts with existing lock", "335544477": "Maximum indexes per table (@1) exceeded", "335544478": "enable journal for database before starting online dump", "335544479": "online dump failure. Retry dump", "335544480": "an online dump is already in progress", "335544481": "no more disk/tape space.  Cannot continue online dump", "335544482": "journaling allowed only if database has Write-ahead Log", "335544483": "maximum number of online dump files that can be specified is 16", "335544484": "error in opening Write-ahead Log file during recovery", "335544485": "Invalid statement handle", "335544486": "Write-ahead log subsystem failure", "335544487": "WAL Writer error", "335544488": "Log file header of @1 too small", "335544489": "Invalid version of log file @1", "335544490": "Log file @1 not latest in the chain but open flag still set", "335544491": "Log file @1 not closed properly; database recovery may be required", "335544492": "Database name in the log file @1 is different", "335544493": "Unexpected end of log file @1 at offset @2", "335544494": "Incomplete log record at offset @1 in log file @2", "335544495": "Log record header too small at offset @1 in log file @", "335544496": "Log block too small at offset @1 in log file @2", "335544497": "Illegal attempt to attach to an uninitialized WAL segment for @1", "335544498": "Invalid WAL parameter block option @1", "335544499": "Cannot roll over to the next log file @1", "335544500": "Database does not use Write-ahead Log", "335544501": "cannot drop log file when journaling is enabled", "335544502": "Reference to invalid stream number", "335544503": "WAL subsystem encountered error", "335544504": "WAL subsystem corrupted", "335544505": "must specify archive file when enabling long term journal for databases with round-robin log files", "335544506": "Database @1 shutdown in progress", "335544507": "Refresh range number @1 already in use", "335544508": "Refresh range number @1 not found", "335544509": "CHARACTER SET @1 is not defined", "335544510": "Lock time-out on wait transaction", "335544511": "Procedure @1 is not defined", "335544512": "Input parameter mismatch for procedure @1", "335544513": "Database @1: WAL subsystem bug for pid @2\n@3", "335544514": "Could not expand the WAL segment for database @1", "335544515": "Status code @1 unknown", "335544516": "Exception @1 not defined", "335544517": "Exception @1", "335544518": "Restart shared cache manager", "335544519": "Invalid lock handle", "335544520": "long-term journaling already enabled", "335544521": "Unable to roll over please see Firebird log.", "335544522": "WAL I/O error.  Please see Firebird log.", "335544523": "WAL writer - Journal server communication error.  Please see Firebird log.", "335544524": "WAL buffers cannot be increased.  Please see Firebird log.", "335544525": "WAL setup error.  Please see Firebird log.", "335544526": "obsolete", "335544527": "Cannot start WAL writer for the database @1", "335544528": "Database @1 shutdown", "335544529": "Cannot modify an existing user privilege", "335544530": "Cannot delete PRIMARY KEY being used in FOREIGN KEY definition", "335544531": "Column used in a PRIMARY constraint must be NOT NULL", "335544532": "Name of Referential Constraint not defined in constraints table", "335544533": "Non-existent PRIMARY or UNIQUE KEY specified for FOREIGN KEY", "335544534": "Cannot update constraints (RDB$REF_CONSTRAINTS)", "335544535": "Cannot update constraints (RDB$CHECK_CONSTRAINTS)", "335544536": "Cannot delete CHECK constraint entry (RDB$CHECK_CONSTRAINTS)", "335544537": "Cannot delete index segment used by an Integrity Constraint", "335544538": "Cannot update index segment used by an Integrity Constraint", "335544539": "Cannot delete index used by an Integrity Constraint", "335544540": "Cannot modify index used by an Integrity Constraint", "335544541": "Cannot delete trigger used by a CHECK Constraint", "335544542": "Cannot update trigger used by a CHECK Constraint", "335544543": "Cannot delete column being used in an Integrity Constraint", "335544544": "Cannot rename column being used in an Integrity Constraint", "335544545": "Cannot update constraints (RDB$RELATION_CONSTRAINTS)", "335544546": "Cannot define constraints on views", "335544547": "Internal gds software consistency check (invalid RDB$CONSTRAINT_TYPE)", "335544548": "Attempt to define a second PRIMARY KEY for the same table", "335544549": "Cannot modify or erase a system trigger", "335544550": "Only the owner of a table may reassign ownership", "335544551": "Could not find table/procedure for GRANT", "335544552": "Could not find column for GRANT", "335544553": "User does not have GRANT privileges for operation", "335544554": "Table/procedure has non-SQL security class defined", "335544555": "Column has non-SQL security class defined", "335544556": "Write-ahead Log without shared cache configuration not allowed", "335544557": "Database shutdown unsuccessful", "335544558": "Operation violates check constraint @1 on view or table @2", "335544559": "Invalid service handle", "335544560": "Database @1 shutdown in @2 seconds", "335544561": "Wrong version of service parameter block", "335544562": "Unrecognized service parameter block", "335544563": "Service @1 is not defined", "335544564": "long-term journaling not enabled", "335544565": "Cannot transliterate character between character sets", "335544566": "WAL defined; Cache Manager must be started first", "335544567": "Overflow log specification required for round-robin log", "335544568": "Implementation of text subtype @1 not located", "335544569": "Dynamic SQL Error", "335544570": "Invalid command", "335544571": "Data type for constant unknown", "335544572": "Invalid cursor reference", "335544573": "Data type unknown", "335544574": "Invalid cursor declaration", "335544575": "Cursor @1 is not updatable", "335544576": "Attempt to reopen an open cursor", "335544577": "Attempt to reclose a closed cursor", "335544578": "Column unknown", "335544579": "Internal error", "335544580": "Table unknown", "335544581": "Procedure unknown", "335544582": "Request unknown", "335544583": "SQLDA missing or incorrect version, or incorrect number/type of variables", "335544584": "Count of read - write columns does not equal count of values", "335544585": "Invalid statement handle", "335544586": "Function unknown", "335544587": "Column is not a BLOB", "335544588": "COLLATION @1 for CHARACTER SET @2 is not defined", "335544589": "COLLATION @1 is not valid for specified CHARACTER SET", "335544590": "Option specified more than once", "335544591": "Unknown transaction option", "335544592": "Invalid array reference", "335544593": "<PERSON><PERSON><PERSON> declared with too many dimensions", "335544594": "Illegal array dimension range", "335544595": "Trigger unknown", "335544596": "Subselect illegal in this context", "335544597": "Cannot prepare a CREATE DATABASE/SCHEMA statement", "335544598": "Must specify column name for view select expression", "335544599": "Number of columns does not match select list", "335544600": "Only simple column names permitted for VIEW WITH CHECK OPTION", "335544601": "No WHERE clause for VIEW WITH CHECK OPTION", "335544602": "Only one table allowed for VIEW WITH CHECK OPTION", "335544603": "DISTINCT, GROUP or HAVING not permitted for VIEW WITH CHECK OPTION", "335544604": "FOREIGN KEY column count does not match PRIMARY KEY", "335544605": "No subqueries permitted for VIEW WITH CHECK OPTION", "335544606": "Expression evaluation not supported", "335544607": "Gen.c: node not supported", "335544608": "Unexpected end of command", "335544609": "INDEX @1", "335544610": "EXCEPTION @1", "335544611": "COLUMN @1", "335544612": "Token unknown", "335544613": "Union not supported", "335544614": "Unsupported DSQL construct", "335544615": "Column used with aggregate", "335544616": "Invalid column reference", "335544617": "Invalid ORDER BY clause", "335544618": "Return mode by value not allowed for this data type", "335544619": "External functions cannot have morethan 10 parametrs", "335544620": "Alias @1 conflicts with an alias in the same statement", "335544621": "Alias @1 conflicts with a procedure in the same statement", "335544622": "Alias @1 conflicts with a table in the same statement", "335544623": "Illegal use of keyword VALUE", "335544624": "Segment count of 0 defined for index @1", "335544625": "A node name is not permitted in a secondary, shadow, cache or log file name", "335544626": "TABLE @1", "335544627": "PROCEDURE @1", "335544628": "Cannot create index @1", "335544629": "Write-ahead Log with shadowing configuration not allowed", "335544630": "There are @1 dependencies", "335544631": "Too many keys defined for index @1", "335544632": "Preceding file did not specify length, so @1 must include starting page number", "335544633": "Shadow number must be a positive integer", "335544634": "Token unknown - line @1, column @2", "335544635": "There is no alias or table named @1 at this scope level", "335544636": "There is no index @1 for table @2", "335544637": "Table @1 is not referenced in plan", "335544638": "Table @1 is referenced more than once in plan; use aliases to distinguish", "335544639": "Table @1 is referenced in the plan but not the from list", "335544640": "Invalid use of CHARACTER SET or COLLATE", "335544641": "Specified domain or source column @1 does not exist", "335544642": "Index @1 cannot be used in the specified plan", "335544643": "The table @1 is referenced twice; use aliases to differentiate", "335544644": "Illegal operation when at beginning of stream", "335544645": "The current position is on a crack", "335544646": "Database or file exists", "335544647": "Invalid comparison operator for find operation", "335544648": "Connection lost to pipe server", "335544649": "Bad checksum", "335544650": "Wrong page type", "335544651": "Cannot insert because the file is readonly or is on a read only medium", "335544652": "Multiple rows in singleton select", "335544653": "Cannot attach to password database", "335544654": "Cannot start transaction for password database", "335544655": "Invalid direction for find operation", "335544656": "Variable @1 conflicts with parameter in same procedure", "335544657": "Array/BLOB/DATE data types not allowed in arithmetic", "335544658": "@1 is not a valid base table of the specified view", "335544659": "Table @1 is referenced twice in view; use an alias to distinguish", "335544660": "View @1 has more than one base table; use aliases to distinguish", "335544661": "Cannot add index, index root page is full", "335544662": "BLOB SUB_TYPE @1 is not defined", "335544663": "Too many concurrent executions of the same request", "335544664": "Duplicate specification of @1- not supported", "335544665": "Violation of PRIMARY or UNIQUE KEY constraint \"@1\" on table \"@2\"", "335544666": "Server version too old to support all CREATE DATABASE options", "335544667": "Drop database completed with errors", "335544668": "Procedure @1 does not return any values", "335544669": "Count of column list and variable list do not match", "335544670": "Attempt to index BLOB column in index @1", "335544671": "Attempt to index array column in index @1", "335544672": "Too few key columns found for index @1 (incorrect column name?)", "335544673": "Cannot delete", "335544674": "Last column in a table cannot be deleted", "335544675": "Sort error", "335544676": "Sort error: not enough memory", "335544677": "Too many versions", "335544678": "Invalid key position", "335544679": "Segments not allowed in expression index @1", "335544680": "Sort error: corruption in data structure", "335544681": "New record size of @1 bytes is too big", "335544682": "Inappropriate self-reference of column", "335544683": "Request depth exceeded. (Recursive definition?)", "335544684": "Cannot access column @1 in view @2", "335544685": "Dbkey not available for multi - table views", "335544686": "journal file wrong format", "335544687": "intermediate journal file full", "335544688": "The prepare statement identifies a prepare statement with an open cursor", "335544689": "Firebird error", "335544690": "<PERSON><PERSON> redefined", "335544691": "Insufficient memory to allocate page buffer cache", "335544692": "Log redefined", "335544693": "Log size too small", "335544694": "Log partition size too small", "335544695": "Partitions not supported in series of log file specification", "335544696": "Total length of a partitioned log must be specified", "335544697": "Precision must be from 1 to 18", "335544698": "Scale must be between zero and precision", "335544699": "Short integer expected", "335544700": "Long integer expected", "335544701": "Unsigned short integer expected", "335544702": "Invalid ESCAPE sequence", "335544703": "Service @1 does not have an associated executable", "335544704": "Failed to locate host machine", "335544705": "Undefined service @1/@2", "335544706": "The specified name was not found in the hosts file or Domain Name Services", "335544707": "User does not have GRANT privileges on base table/view for operation", "335544708": "Ambiguous column reference", "335544709": "Invalid aggregate reference", "335544710": "Navigational stream @1 references a view with more than one base table", "335544711": "Attempt to execute an unprepared dynamic SQL statement", "335544712": "Positive value expected", "335544713": "Incorrect values within SQLDA structure", "335544714": "Invalid blob id", "335544715": "Operation not supported for EXTERNAL FILE table @1", "335544716": "Service is currently busy: @1", "335544717": "Stack size insufficent to execute current request", "335544718": "Invalid key for find operation", "335544719": "Error initializing the network software.", "335544720": "Unable to load required library @1.", "335544721": "Unable to complete network request to host \"@1\"", "335544722": "Failed to establish a connection", "335544723": "Error while listening for an incoming connection", "335544724": "Failed to establish a secondary connection for event processing", "335544725": "Error while listening for an incoming event connection request", "335544726": "Error reading data from the connection", "335544727": "Error writing data to the connection", "335544728": "Cannot deactivate index used by an integrity constraint", "335544729": "Cannot deactivate index used by a PRIMARY/UNIQUE constraint", "335544730": "Client/Server Express not supported in this release", "335544731": "[no associated message]", "335544732": "Access to databases on file servers is not supported", "335544733": "Error while trying to create file", "335544734": "Error while trying to open file", "335544735": "Error while trying to close file", "335544736": "Error while trying to read from file", "335544737": "Error while trying to write to file", "335544738": "Error while trying to delete file", "335544739": "Error while trying to access file", "335544740": "A fatal exception occurred during the execution of a user defined function", "335544741": "Connection lost to database", "335544742": "User cannot write to RDB$USER_PRIVILEGES", "335544743": "Token size exceeds limit", "335544744": "Maximum user count exceeded.Contact your database administrator", "335544745": "Your login @1 is same as one of the SQL role name. Ask your\ndatabase administrator to set up a valid Firebird login.\n", "335544746": "\"REFERENCES table\" without \"(column)\" requires PRIMARY KEY on referenced table", "335544747": "The username entered is too long. Maximum length is 31 bytes", "335544748": "The password specified is too long. Maximum length is @1 bytes", "335544749": "A username is required for this operation", "335544750": "A password is required for this operation", "335544751": "The network protocol specified is invalid", "335544752": "A duplicate user name was found in the security database", "335544753": "The user name specified was not found in the security database", "335544754": "An error occurred while attempting to add the user", "335544755": "An error occurred while attempting to modify the user record", "335544756": "An error occurred while attempting to delete the user record", "335544757": "An error occurred while updating the security database", "335544758": "Sort record size of @1 bytes is too big ????", "335544759": "Can not define a not null column with NULL as default value", "335544760": "Invalid clause - '@1'", "335544761": "Too many open handles to database", "335544762": "size of optimizer block exceeded", "335544763": "A string constant is delimited by double quotes", "335544764": "DATE must be changed to TIMESTAMP", "335544765": "Attempted update on read - only database", "335544766": "SQL dialect @1 is not supported in this database", "335544767": "A fatal exception occurred during the execution of a blob filter", "335544768": "Access violation.The code attempted to access a virtual address without privilege to do so", "335544769": "Datatype misalignment.The attempted to read or write a value that was not\nstored on a memory boundary\n", "335544770": "Array bounds exceeded. The code attempted to access an array element that is\nout of bounds.\n", "335544771": "Float denormal operand.One of the floating-point operands is too small to\nrepresent a standard float value.\n", "335544772": "Floating-point divide by zero.The code attempted to divide a floating-point\nvalue by zero.\n", "335544773": "Floating-point inexact result.The result of a floating-point operation cannot\nbe represented as a decimal fraction\n", "335544774": "Floating-point invalid operand.An indeterminant error occurred during a\nfloating-point operation\n", "335544775": "Floating-point overflow.The exponent of a floating-point operation is\ngreater than the magnitude allowed\n", "335544776": "Floating-point stack check.The stack overflowed or underflowed as the\nresult of a floating-point operation\n", "335544777": "Floating-point underflow.The exponent of a floating-point operation is\nless than the magnitude allowed\n", "335544778": "Integer divide by zero.The code attempted to divide an integer value by\nan integer divisor of zero\n", "335544779": "Integer overflow.The result of an integer operation caused the most\nsignificant bit of the result to carry\n", "335544780": "An exception occurred that does not have a description.Exception number @1", "335544781": "Stack overflow.The resource requirements of the runtime stack have exceeded\nthe memory available to it\n", "335544782": "Segmentation Fault. The code attempted to access memory without privileges", "335544783": "Illegal Instruction. The Code attempted to perfrom an illegal operation", "335544784": "Bus Error. The Code caused a system bus error", "*********": "Floating Point Error. The Code caused an Arithmetic Exception\nor a floating point exception\n", "*********": "Cannot delete rows from external files", "*********": "Cannot update rows in external files", "*********": "Unable to perform operation.You must be either SYSDBA or\nowner of the database\n", "*********": "Specified EXTRACT part does not exist in input datatype", "*********": "Service @1 requires SYSDBA permissions. Reattach to the Service Manager using the SYSDBA account", "*********": "The file @1 is currently in use by another process.Try again later", "*********": "Cannot attach to services manager", "*********": "Metadata update statement is not allowed by the current database SQL dialect @1", "*********": "Operation was cancelled", "*********": "Unexpected item in service parameter block, expected @1", "*********": "Client SQL dialect @1 does not support reference to @2 datatype", "*********": "User name and password are required while attaching to\nthe services manager\n", "*********": "You created an indirect dependency on uncommitted metadata. You must\nroll back the current transaction\n", "*********": "The service name was not specified", "*********": "Too many Contexts of Relation/Procedure/Views. Maximum allowed is 255", "*********": "Data type not supported for arithmetic", "*********": "Database dialect being changed from 3 to 1", "*********": "Database dialect not changed", "*********": "Unable to create database @1", "*********": "Database dialect @1 is not a valid dialect", "335544806": "Valid database dialects are @1", "335544807": "SQL warning code = @1", "335544808": "DATE data type is now called TIMESTAMP", "335544809": "Function @1 is in @2, which is not in a permitted directory for\nexternal functions\n", "335544810": "Value exceeds the range for valid dates", "335544811": "Passed client dialect @1 is not a valid dialect", "335544812": "Valid client dialects are @1", "335544813": "Unsupported field type specified in BETWEEN predicate", "335544814": "Services functionality will be supported in a later version\nof the product\n", "335544815": "GENERATOR @1", "335544816": "UDF @1", "335544817": "Invalid parameter to FIRST.Only integers >= 0 are allowed", "335544818": "Invalid parameter to SKIP. Only integers >= 0 are allowed", "335544819": "File exceeded maximum size of 2GB. Add another database file or use\na 64 bit I/O version of Firebird\n", "335544820": "Unable to find savepoint with name @1 in transaction context", "335544821": "Invalid column position used in the @1 clause", "335544822": "Cannot use an aggregate function in a WHERE clause, use HAVING instead", "335544823": "Cannot use an aggregate function in a GROUP BY clause", "335544824": "Invalid expression in the @1 (not contained in either an aggregate function or the GROUP BY clause)", "335544825": "Invalid expression in the @1 (neither an aggregate function nor a part of the GROUP BY clause)", "335544826": "Nested aggregate functions are not allowed", "335544827": "Invalid argument in EXECUTE STATEMENT-cannot convert to string", "335544828": "Wrong request type in EXECUTE STATEMENT '@1'", "335544829": "Variable type (position @1) in EXECUTE STATEMENT '@2' INTO does not\nmatch returned column type\n", "335544830": "Too many recursion levels of EXECUTE STATEMENT", "335544831": "Access to @1 \"@2\" is denied by server administrator", "335544832": "Cannot change difference file name while database is in backup mode", "335544833": "Physical backup is not allowed while Write-Ahead Log is in use", "335544834": "Cursor is not open", "335544835": "Target shutdown mode is invalid for database \"@1\"", "335544836": "Concatenation overflow. Resulting string cannot exceed 32K in length", "335544837": "Invalid offset parameter @1 to SUBSTRING. Only positive integers are allowed", "*********": "Foreign key reference target does not exist", "*********": "Foreign key references are present for the record", "*********": "Cannot update", "*********": "Cursor is already open", "*********": "@1", "*********": "Context variable @1 is not found in namespace @2", "*********": "Invalid namespace name @1 passed to @2", "*********": "Too many context variables", "*********": "Invalid argument passed to @1", "*********": "BLR syntax error. Identifier @1... is too long", "*********": "Exception @1", "*********": "Malformed string", "*********": "Output parameter mismatch for procedure @1", "*********": "Unexpected end of command- line @1, column @2", "*********": "Partner index segment no @1 has incompatible data type", "*********": "Invalid length parameter @1 to SUBSTRING. Negative integers are not allowed", "*********": "CHARACTER SET @1 is not installed", "*********": "COLLATION @1 for CHARACTER SET @2 is not installed", "*********": "Connection shutdown", "*********": "Maximum BLOB size exceeded", "*********": "Can't have relation with only computed fields or constraints", "*********": "Time precision exceeds allowed range (0-@1)", "*********": "Unsupported conversion to target type BLOB (subtype @1)", "*********": "Unsupported conversion to target type ARRAY", "*********": "Stream does not support record locking", "*********": "Cannot create foreign key constraint @1. Partner index does not\nexist or is inactive\n", "335544864": "Transactions count exceeded. Perform backup and restore to make\ndatabase operable again\n", "335544865": "Column has been unexpectedly deleted", "335544866": "@1 cannot depend on @2", "335544867": "Blob sub_types bigger than 1 (text) are for internal use only", "335544868": "Procedure @1 is not selectable (it does not contain a SUSPEND\nstatement)\n", "335544869": "Datatype @1 is not supported for sorting operation", "335544870": "COLLATION @1", "335544871": "DOMAIN @1", "335544872": "Domain @1 is not defined", "335544873": "Array data type can use up to @1 dimensions", "335544874": "A multi database transaction cannot span more than @1 databases", "335544875": "Bad debug info format", "335544876": "Error while parsing procedure @1' s B<PERSON>", "335544877": "Index key too big", "335544878": "Concurrent transaction number is @1", "335544879": "Validation error for variable @1, value \"@2\"", "335544880": "Validation error for @1, value \"@2\"", "335544881": "Difference file name should be set explicitly for database on raw device", "335544882": "Login name too long (@1 characters, maximum allowed @2)", "335544883": "Column @1 is not defined in procedure @2", "335544884": "Invalid SIMILAR TO pattern", "335544885": "Invalid TEB format", "335544886": "Found more than one transaction isolation in TPB", "335544887": "Table reservation lock type @1 requires table name before in TPB", "335544888": "Found more than one @1 specification in TPB", "335544889": "Option @1 requires READ COMMITTED isolation in TPB", "335544890": "Option @1 is not valid if @2 was used previously in TPB", "335544891": "Table name length missing after table reservation @1 in TPB", "335544892": "Table name length @1 is too long after table reservation @2 in TPB", "335544893": "Table name length @1 without table name after table reservation @2 in TPB", "335544894": "Table name length @1 goes beyond the remaining TPB size after table reservation @2", "335544895": "Table name length is zero after table reservation @1 in TPB", "335544896": "Table or view @1 not defined in system tables after table reservation @2 in TPB", "335544897": "Base table or view @1 for view @2 not defined in system tables after table reservation @3 in TPB", "335544898": "Option length missing after option @1 in TPB", "335544899": "Option length @1 without value after option @2 in TPB", "335544900": "Option length @1 goes beyond the remaining TPB size after option @2", "335544901": "Option length is zero after table reservation @1 in TPB", "335544902": "Option length @1 exceeds the range for option @2 in TPB", "335544903": "Option value @1 is invalid for the option @2 in TPB", "335544904": "Preserving previous table reservation @1 for table @2, stronger than new @3 in TPB", "335544905": "Table reservation @1 for table @2 already specified and is stronger than new @3 in TPB", "335544906": "Table reservation reached maximum recursion of @1 when expanding views in TPB", "335544907": "Table reservation in TPB cannot be applied to @1 because it’s a virtual table", "335544908": "Table reservation in TPB cannot be applied to @1 because it’s a system table", "335544909": "Table reservation @1 or @2 in TPB cannot be applied to @3 because it’s a temporary table", "335544910": "Cannot set the transaction in read only mode after a table reservation isc_tpb_lock_write in TPB", "335544911": "Cannot take a table reservation isc_tpb_lock_write in TPB because the transaction is in read only mode", "335544912": "value exceeds the range for a valid time", "335544913": "value exceeds the range for valid timestamps", "335544914": "string right truncation", "335544915": "blob truncation when converting to a string: length limit exceeded", "335544916": "numeric value is out of range", "335544917": "Firebird shutdown is still in progress after the specified timeout", "335544918": "Attachment handle is busy", "335544919": "Bad written UDF detected: pointer returned in FREE_IT function was not allocated by ib_util_malloc", "335544920": "External Data Source provider '@1' not found", "335544921": "Execute statement error at @1 :\n@2Data source : @3", "335544922": "Execute statement preprocess SQL error", "335544923": "Statement expected", "335544924": "Parameter name expected", "335544925": "Unclosed comment found near '@1'", "335544926": "Execute statement error at @1 :\n@2Statement : @3\nData source : @4", "335544927": "Input parameters mismatch", "335544928": "Output parameters mismatch", "335544929": "Input parameter '@1' have no value set", "335544930": "BLR stream length @1 exceeds implementation limit @2", "335544931": "Monitoring table space exhausted", "335544932": "module name or entrypoint could not be found", "335544933": "nothing to cancel", "335544934": "ib_util library has not been loaded to deallocate memory returned by FREE_IT function", "335544935": "Cannot have circular dependencies with computed fields", "335544936": "Security database error", "335544937": "Invalid data type in DATE/TIME/TIMESTAMP addition or subtraction in add_datettime()", "335544938": "Only a TIME value can be added to a DATE value", "335544939": "Only a DATE value can be added to a TIME value", "335544940": "TIMESTAMP values can be subtracted only from another TIMESTAMP value", "335544941": "Only one operand can be of type TIMESTAMP", "335544942": "Only HOUR, MINUTE, SECOND and MILLISECOND can be extracted from TIME values", "335544943": "HOUR, MINUTE, SECOND and MILLISECOND cannot be extracted from DATE values", "335544944": "Invalid argument for EXTRACT() not being of DATE/TIME/TIMESTAMP type", "335544945": "Arguments for @1 must be integral types or NUMERIC/DECIMAL without scale", "335544946": "First argument for @1 must be integral type or floating point type", "335544947": "Human readable UUID argument for @1 must be of string type", "335544948": "Human readable UUID argument for @2 must be of exact length @1", "335544949": "Human readable UUID argument for @3 must have \"-\" at position @2 instead of \"@1\"", "335544950": "Human readable UUID argument for @3 must have hex digit at position @2 instead of \"@1\"", "335544951": "Only HOUR, MINUTE, SECOND and MILLISECOND can be added to TIME values in @1", "335544952": "Invalid data type in addition of part to DATE/TIME/TIMESTAMP in @1", "335544953": "Invalid part @1 to be added to a DATE/TIME/TIMESTAMP value in @2", "335544954": "Expected DATE/TIME/TIMESTAMP type in evlDateAdd() result", "335544955": "Expected DATE/TIME/TIMESTAMP type as first and second argument to @1", "335544956": "The result of TIME-<value> in @1 cannot be expressed in YEAR, MONTH, DAY or WEEK", "335544957": "The result of TIME-TIMESTAMP or TIMESTAMP-TIME in @1 cannot be expressed in HOUR, MINUTE, SECOND or MILLISECOND", "335544958": "The result of DATE-TIME or TIME-DATE in @1 cannot be expressed in HOUR, MINUTE, SECOND and MILLISECOND", "335544959": "Invalid part @1 to express the difference between two DATE/TIME/TIMESTAMP values in @2", "335544960": "Argument for @1 must be positive", "335544961": "Base for @1 must be positive", "335544962": "Argument #@1 for @2 must be zero or positive", "335544963": "Argument #@1 for @2 must be positive", "335544964": "Base for @1 cannot be zero if exponent is negative", "335544965": "Base for @1 cannot be negative if exponent is not an integral value", "335544966": "The numeric scale must be between -128 and 127 in @1", "335544967": "Argument for @1 must be zero or positive", "335544968": "Binary UUID argument for @1 must be of string type", "335544969": "Binary UUID argument for @2 must use @1 bytes", "335544970": "Missing required item @1 in service parameter block", "335544971": "@1 server is shutdown", "335544972": "Invalid connection string", "335544973": "Unrecognized events block", "335544974": "Could not start first worker thread - shutdown server", "335544975": "Timeout occurred while waiting for a secondary connection for event processing", "335544976": "Argument for @1 must be different than zero", "335544977": "Argument for @1 must be in the range [-1, 1]", "335544978": "Argument for @1 must be greater or equal than one", "335544979": "Argument for @1 must be in the range ]-1, 1[", "335544980": "Incorrect parameters provided to internal function @1", "335544981": "Floating point overflow in built-in function @1", "335544982": "Floating point overflow in result from UDF @1", "335544983": "Invalid floating point value returned by UDF @1", "335544984": "Database is probably already opened by another engine instance in another Windows session", "335544985": "No free space found in temporary directories", "335544986": "Explicit transaction control is not allowed", "335544987": "Use of TRUSTED switches in spb_command_line is prohibited", "335544988": "PACKAGE @1", "335544989": "Cannot make field @1 of table @2 NOT NULL because there are NULLs present", "335544990": "Feature @1 is not supported anymore", "335544991": "VIEW @1", "335544992": "Can not access lock files directory @1", "335544993": "Fetch option @1 is invalid for a non-scrollable cursor", "335544994": "Error while parsing function @1’s BLR", "335544995": "Cannot execute function @1 of the unimplemented package @2", "335544996": "Cannot execute procedure @1 of the unimplemented package @2", "335544997": "External function @1 not returned by the external engine plugin @2", "335544998": "External procedure @1 not returned by the external engine plugin @2", "335544999": "External trigger @1 not returned by the external engine plugin @2", "335545000": "Incompatible plugin version @1 for external engine @2", "335545001": "External engine @1 not found", "335545002": "Attachment is in use", "335545003": "Transaction is in use", "335545004": "Error loading plugin @1", "335545005": "Loadable module @1 not found", "335545006": "Standard plugin entrypoint does not exist in module @1", "335545007": "Module @1 exists but can not be loaded", "335545008": "Module @1 does not contain plugin @2 type @3", "335545009": "Invalid usage of context namespace DDL_TRIGGER", "335545010": "Value is NULL but isNull parameter was not informed", "335545011": "Type @1 is incompatible with BLOB", "335545012": "Invalid date", "335545013": "Invalid time", "335545014": "Invalid timestamp", "335545015": "Invalid index @1 in function @2", "335545016": "@1", "335545017": "Asynchronous call is already running for this attachment", "335545018": "Function @1 is private to package @2", "335545019": "Procedure @1 is private to package @2", "335545020": "Request can’t access new records in relation @1 and should be recompiled", "335545021": "invalid events id (handle)", "335545022": "Cannot copy statement @1", "335545023": "Invalid usage of boolean expression", "335545024": "Arguments for @1 cannot both be zero", "335545025": "missing service ID in spb", "335545026": "External BLR message mismatch: invalid null descriptor at field @1", "335545027": "External BLR message mismatch: length = @1, expected @2", "335545028": "Subscript @1 out of bounds [@2, @3]", "335545029": "Install incomplete. To complete security database initialization please CREATE USER. For details read doc/README.security_database.txt.", "335545030": "@1 operation is not allowed for system table @2", "335545031": "Libtommath error code @1 in function @2", "335545032": "unsupported BLR version (expected between @1 and @2, encountered @3)", "335545033": "expected length @1, actual @2", "335545034": "Wrong info requested in isc_svc_query() for anonymous service", "335545035": "No isc_info_svc_stdin in user request, but service thread requested stdin data", "335545036": "Start request for anonymous service is impossible", "335545037": "All services except for getting server log require switches", "335545038": "Size of stdin data is more than was requested from client", "335545039": "Crypt plugin @1 failed to load", "335545040": "Length of crypt plugin name should not exceed @1 bytes", "335545041": "Crypt failed - already crypting database", "335545042": "Crypt failed - database is already in requested state", "335545043": "Missing crypt plugin, but page appears encrypted", "335545044": "No providers loaded", "335545045": "NULL data with non-zero SPB length", "335545046": "Maximum (@1) number of arguments exceeded for function @2", "335545047": "External BLR message mismatch: names count = @1, blr count = @2", "335545048": "External BLR message mismatch: name @1 not found", "335545049": "Invalid resultset interface", "335545050": "Message length passed from user application does not match set of columns", "335545051": "Resultset is missing output format information", "335545052": "Message metadata not ready - item @1 is not finished", "335545053": "Missing configuration file: @1", "335545054": "@1: illegal line <@2>", "335545055": "Invalid include operator in @1 for <@2>", "335545056": "Include depth too big", "335545057": "File to include not found", "335545058": "Only the owner can change the ownership", "335545059": "undefined variable number", "335545060": "Missing security context for @1", "335545061": "Missing segment @1 in multisegment connect block parameter", "335545062": "Different logins in connect and attach packets - client library error", "335545063": "Exceeded exchange limit during authentication handshake", "335545064": "Incompatible wire encryption levels requested on client and server", "335545065": "Client attempted to attach unencrypted but wire encryption is required", "335545066": "Client attempted to start wire encryption using unknown key @1", "335545067": "Client attempted to start wire encryption using unsupported plugin @1", "335545068": "Error getting security database name from configuration file", "335545069": "Client authentication plugin is missing required data from server", "335545070": "Client authentication plugin expected @2 bytes of @3 from server, got @1", "335545071": "Attempt to get information about an unprepared dynamic SQL statement.", "335545072": "Problematic key value is @1", "335545073": "Cannot select virtual table @1 for update WITH LOCK", "335545074": "Cannot select system table @1 for update WITH LOCK", "335545075": "Cannot select temporary table @1 for update WITH LOCK", "335545076": "System @1 @2 cannot be modified", "335545077": "Server misconfigured - contact administrator please", "335545078": "Deprecated backward compatibility ALTER ROLE …​ SET/DROP AUTO ADMIN mapping may be used only for RDB$ADMIN role", "335545079": "Mapping @1 already exists", "335545080": "Mapping @1 does not exist", "335545081": "@1 failed when loading mapping cache", "335545082": "Invalid name <*> in authentication block", "335545083": "Multiple maps found for @1", "335545084": "Undefined mapping result - more than one different results found", "335545085": "Incompatible mode of attachment to damaged database", "335545086": "Attempt to set in database number of buffers which is out of acceptable range [@1:@2]", "335545087": "Attempt to temporarily set number of buffers less than @1", "335545088": "Global mapping is not available when database @1 is not present", "335545089": "Global mapping is not available when table RDB$MAP is not present in database @1", "335545090": "Your attachment has no trusted role", "335545091": "Role @1 is invalid or unavailable", "335545092": "Cursor @1 is not positioned in a valid record", "335545093": "Duplicated user attribute @1", "335545094": "There is no privilege for this operation", "335545095": "Using GRANT OPTION on @1 not allowed", "335545096": "read conflicts with concurrent update", "335545097": "@1 failed when working with CREATE DATABASE grants", "335545098": "CREATE DATABASE grants check is not possible when database @1 is not present", "335545099": "CREATE DATABASE grants check is not possible when table RDB$DB_CREATORS is not present in database @1", "335545100": "Interface @3 version too old: expected @1, found @2", "335545101": "Input parameter mismatch for function @1", "335545102": "Error during savepoint backout - transaction invalidated", "335545103": "Domain used in the PRIMARY KEY constraint of table @1 must be NOT NULL", "335545104": "CHARACTER SET @1 cannot be used as a attachment character set", "335545105": "Some database(s) were shutdown when trying to read mapping data", "335545106": "Error occurred during login, please check server firebird.log for details", "335545107": "Database already opened with engine instance, incompatible with current", "335545108": "Invalid crypt key @1", "335545109": "Page requires encryption but crypt plugin is missing", "335545110": "Maximum index depth (@1 levels) is reached", "335545111": "System privilege @1 does not exist", "335545112": "System privilege @1 is missing", "335545113": "Invalid or missing checksum of encrypted database", "335545114": "You must have SYSDBA rights at this server", "335545115": "Cannot open cursor for non-SELECT statement", "335545116": "If <window frame bound 1> specifies @1, then <window frame bound 2> shall not specify @2", "335545117": "RANGE based window with <expr> {PRECEDING | FOLLOWING} cannot have ORDER BY with more than one value", "335545118": "RANGE based window must have an ORDER BY key of numerical, date, time or timestamp types", "335545119": "Window RANGE/ROWS PRECEDING/FOLLOWING value must be of a numerical type", "335545120": "Invalid PRECEDING or FOLLOWING offset in window function: cannot be negative", "335545121": "Window @1 not found", "335545122": "Cannot use PARTITION BY clause while overriding the window @1", "335545123": "Cannot use ORDER BY clause while overriding the window @1 which already has an ORDER BY clause", "335545124": "Cannot override the window @1 because it has a frame clause. Tip: it can be used without parenthesis in OVER", "335545125": "Duplicate window definition for @1", "335545126": "SQL statement is too long. Maximum size is @1 bytes.", "335545127": "Config level timeout expired.", "335545128": "Attachment level timeout expired.", "335545129": "Statement level timeout expired.", "335545130": "Killed by database administrator.", "335545131": "Idle timeout expired.", "335545132": "Database is shutdown.", "335545133": "Engine is shutdown.", "335545134": "OVERRIDING clause can be used only when an identity column is present in the INSERT’s field list for table/view @1", "335545135": "OVERRIDING SYSTEM VALUE can be used only for identity column defined as 'GENERATED ALWAYS' in INSERT for table/view @1", "335545136": "OVERRIDING USER VALUE can be used only for identity column defined as 'GENERATED BY DEFAULT' in INSERT for table/view @1", "335545137": "OVERRIDING SYSTEM VALUE should be used to override the value of an identity column defined as 'GENERATED ALWAYS' in table/view @1", "335545138": "DecFloat precision must be 16 or 34", "335545139": "Decimal float divide by zero.  The code attempted to divide a DECFLOAT value by zero.", "335545140": "Decimal float inexact result.  The result of an operation cannot be represented as a decimal fraction.", "335545141": "Decimal float invalid operation.  An indeterminant error occurred during an operation.", "335545142": "Decimal float overflow.  The exponent of a result is greater than the magnitude allowed.", "335545143": "Decimal float underflow.  The exponent of a result is less than the magnitude allowed.", "335545144": "Sub-function @1 has not been defined", "335545145": "Sub-procedure @1 has not been defined", "335545146": "Sub-function @1 has a signature mismatch with its forward declaration", "335545147": "Sub-procedure @1 has a signature mismatch with its forward declaration", "335545148": "Default values for parameters are not allowed in definition of the previously declared sub-function @1", "335545149": "Default values for parameters are not allowed in definition of the previously declared sub-procedure @1", "335545150": "Sub-function @1 was declared but not implemented", "335545151": "Sub-procedure @1 was declared but not implemented", "335545152": "Invalid HASH algorithm @1", "335545153": "Expression evaluation error for index \"@1\" on table \"@2\"", "335545154": "Invalid decfloat trap state @1", "335545155": "Invalid decfloat rounding mode @1", "335545156": "Invalid part @1 to calculate the @1 of a DATE/TIMESTAMP", "335545157": "Expected DATE/TIMESTAMP value in @1", "335545158": "Precision must be from @1 to @2", "335545159": "invalid batch handle", "335545160": "Bad international character in tag @1", "335545161": "Null data in parameters block with non-zero length", "335545162": "Items working with running service and getting generic server information should not be mixed in single info block", "335545163": "Unknown information item, code @1", "335545164": "Wrong version of blob parameters block @1, should be @2", "335545165": "User management plugin is missing or failed to load", "335545166": "Missing entrypoint @1 in ICU library", "335545167": "Could not find acceptable ICU library", "335545168": "Name @1 not found in system MetadataBuilder", "335545169": "Parse to tokens error", "335545170": "Error opening international conversion descriptor from @1 to @2", "335545171": "Message @1 is out of range, only @2 messages in batch", "335545172": "Detailed error info for message @1 is missing in batch", "335545173": "Compression stream init error @1", "335545174": "Decompression stream init error @1", "335545175": "Segment size (@1) should not exceed 65535 (64K - 1) when using segmented blob", "335545176": "Invalid blob policy in the batch for @1() call", "335545177": "Can’t change default BPB after adding any data to batch", "335545178": "Unexpected info buffer structure querying for default blob alignment", "335545179": "Duplicated segment @1 in multisegment connect block parameter", "335545180": "Plugin not supported by network protocol", "335545181": "Error parsing message format", "335545182": "Wrong version of batch parameters block @1, should be @2", "335545183": "Message size (@1) in batch exceeds internal buffer size (@2)", "335545184": "<PERSON><PERSON> already opened for this statement", "335545185": "Invalid type of statement used in batch", "335545186": "Statement used in batch must have parameters", "335545187": "There are no blobs in associated with batch statement", "335545188": "appendBlobData() is used to append data to last blob but no such blob was added to the batch", "335545189": "Portions of data, passed as blob stream, should have size multiple to the alignment required for blobs", "335545190": "Repeated blob id @1 in registerBlob()", "335545191": "Blob buffer format error", "335545192": "Unusable (too small) data remained in @1 buffer", "335545193": "Blob continuation should not contain BPB", "335545194": "Size of BPB (@1) greater than remaining data (@2)", "335545195": "Size of segment (@1) greater than current BLOB data (@2)", "335545196": "Size of segment (@1) greater than available data (@2)", "335545197": "Unknown blob ID @1 in the batch message", "335545198": "Internal buffer overflow - batch too big", "335545199": "Numeric literal too long", "335545200": "Error using events in mapping shared memory: @1", "335545201": "Global mapping memory overflow", "335545202": "Header page overflow - too many clumplets on it", "335545203": "No matching client/server authentication plugins configured for execute statement in embedded datasource", "335545204": "Missing database encryption key for your attachment", "335545205": "Key holder plugin @1 failed to load", "335545206": "Cannot reset user session", "335545207": "There are open transactions (@1 active)", "335545208": "Session was reset with warning(s)", "335545209": "Transaction is rolled back due to session reset, all changes are lost", "335545210": "Plugin @1:", "335545211": "PARAMETER @1", "335545212": "Starting page number for file @1 must be @2 or greater", "335545213": "Invalid time zone offset: @1 - must use format +/-hours:minutes and be between -14:00 and +14:00", "335545214": "Invalid time zone region: @1", "335545215": "Invalid time zone ID: @1", "335545216": "Wrong base64 text length @1, should be multiple of 4", "335545217": "Invalid first parameter datatype - need string or blob", "335545218": "Error registering @1 - probably bad tomcrypt library", "335545219": "Unknown crypt algorithm @1 in USING clause", "335545220": "Should specify mode parameter for symmetric cipher", "335545221": "Unknown symmetric crypt mode specified", "335545222": "Mode parameter makes no sense for chosen cipher", "335545223": "Should specify initialization vector (IV) for chosen cipher and/or mode", "335545224": "Initialization vector (IV) makes no sense for chosen cipher and/or mode", "335545225": "Invalid counter endianess @1", "335545226": "Counter endianess parameter is not used in mode @1", "335545227": "Too big counter value @1, maximum @2 can be used", "335545228": "Counter length/value parameter is not used with @1 @2", "335545229": "Invalid initialization vector (IV) length @1, need @2", "335545230": "TomCrypt library error: @1", "335545231": "Starting PRNG yarrow", "335545232": "Setting up PRNG yarrow", "335545233": "Initializing @1 mode", "335545234": "Encrypting in @1 mode", "335545235": "Decrypting in @1 mode", "335545236": "Initializing cipher @1", "335545237": "Encrypting using cipher @1", "335545238": "Decrypting using cipher @1", "335545239": "Setting initialization vector (IV) for @1", "335545240": "Invalid initialization vector (IV) length @1, need  8 or 12", "335545241": "Encoding @1", "335545242": "Decoding @1", "335545243": "Importing RSA key", "335545244": "Invalid OAEP packet", "335545245": "Unknown hash algorithm @1", "335545246": "Making RSA key", "335545247": "Exporting @1 RSA key", "335545248": "RSA-signing data", "335545249": "Verifying RSA-signed data", "335545250": "Invalid key length @1, need 16 or 32", "335545251": "invalid replicator handle", "335545252": "Transaction’s base snapshot number does not exist", "335545253": "Input parameter '@1' is not used in SQL query text", "335545254": "Effective user is @1", "335545255": "Invalid time zone bind mode @1", "335545256": "Invalid decfloat bind mode @1", "335545257": "Invalid hex text length @1, should be multiple of 2", "335545258": "Invalid hex digit @1 at position @2", "335545259": "Error processing isc_dpb_set_bind clumplet \"@1\"", "335545260": "The following statement failed: @1", "335545261": "Can not convert @1 to @2", "335545262": "cannot update old BLOB", "335545263": "cannot read from new BLOB", "335545264": "No permission for CREATE @1 operation", "335545265": "SUSPEND could not be used without RETURNS clause in PROCEDURE or EXECUTE BLOCK", "335545266": "String truncated warning due to the following reason", "335545267": "Monitoring data does not fit into the field", "335545268": "Engine data does not fit into return value of system function", "335545269": "Multiple source records cannot match the same target during MERGE", "335545270": "RDB$PAGES written by non-system transaction, DB appears to be damaged", "335545271": "Replication error", "335545272": "Reset of user session failed. Connection is shut down.", "335545273": "File size is less than expected", "335545274": "Invalid key length @1, need >@2", "335740929": "Database file name (@1) already given", "335740930": "Invalid switch @1", "335740932": "Incompatible switch combination", "335740933": "Replay log pathname required", "335740934": "Number of page buffers for cache required", "335740935": "Numeric value required", "335740936": "Positive numeric value required", "335740937": "Number of transactions per sweep required", "335740940": "\"full\" or \"reserve\" required", "335740941": "User name required", "335740942": "Password required", "335740943": "Subsystem name", "335740945": "Number of seconds required", "335740946": "Numeric value between 0 and 32767 inclusive required", "335740947": "Must specify type of shutdown", "335740948": "Please retry, specifying an option", "335740951": "Please retry, giving a database name", "335740991": "Internal block exceeds maximum size", "335740992": "Corrupt pool", "335740993": "Virtual memory exhausted", "335740994": "Bad pool id.", "335740995": "Transaction state @1 not in valid range", "335741012": "Unexpected end of input", "335741018": "Failed to reconnect to a transaction in database @1", "335741036": "Transaction description item unknown", "335741038": "\"read_only\" or \"read_write\" required", "335741039": "-sql_dialect | set database dialect n", "335741042": "Positive or zero numeric value required", "336003074": "Cannot SELECT RDB$DB_KEY from a stored procedure", "336003075": "Precision 10 to 18 changed from DOUBLE PRECISION in SQL\ndialect 1 to 64-bit scaled integer in SQL dialect 3\n", "336003076": "Use of @1 expression that returns different results in dialect 1 and dialect 3", "336003077": "Database SQL dialect @1 does not support reference to @2 datatype", "336003079": "DB dialect @1 and client dialect @2 conflict with respect to numeric precision @3", "336003080": "WARNING: Numeric literal @1 is interpreted as a floating-point", "336003081": "value in SQL dialect 1, but as an exact numeric value in SQL dialect 3.", "336003082": "WARNING: NUMERIC and DECIMAL fields with precision 10 or greater are stored", "336003083": "as approximate floating-point values in SQL dialect 1, but as 64-bit", "336003084": "integers in SQL dialect 3.", "336003085": "Ambiguous field name between @1 and @2", "336003086": "External function should have return position between 1 and @1", "336003087": "Label @1 @2 in the current scope", "336003088": "Datatypes @1are not comparable in expression @2", "336003089": "Empty cursor name is not allowed", "336003090": "Statement already has a cursor @1 assigned", "336003091": "Cursor @1 is not found in the current context", "336003092": "Cursor @1 already exists in the current context", "336003093": "Relation @1 is ambiguous in cursor @2", "336003094": "Relation @1 is not found in cursor @2", "336003095": "Cursor is not open", "336003096": "Data type @1 is not supported for EXTERNAL TABLES. Relation '@2', field '@3'", "336003097": "Feature not supported on ODS version older than @1.@2", "336003098": "Primary key required on table @1", "336003099": "UPDATE OR INSERT field list does not match primary key of table @1", "336003100": "UPDATE OR INSERT field list does not match MATCHING clause", "336003101": "UPDATE OR INSERT without MATCHING could not be used with views based on more than one table", "336003102": "Incompatible trigger type", "336003103": "Database trigger type can't be changed", "336003104": "To be used with RDB$RECORD_VERSION, @1 must be a table or a view of single table", "336003105": "SQLDA version expected between @1 and @2, found @3", "336003106": "at SQLVAR index @1", "336003107": "empty pointer to NULL indicator variable", "336003108": "empty pointer to data", "336003109": "No SQLDA for input values provided", "336003110": "No SQLDA for output values provided", "336003111": "Wrong number of parameters (expected @1, got @2)", "336003112": "Invalid DROP SQL SECURITY clause", "336003113": "UPDATE OR INSERT value for field @1, part of the implicit or explicit MATCHING clause, cannot be DEFAULT", "336068645": "BLOB Filter @1 not found", "336068649": "Function @1 not found", "336068656": "Index not found", "336068662": "View @1 not found", "336068697": "Domain not found", "336068717": "Triggers created automatically cannot be modified", "336068740": "Table @1 already exists", "336068748": "Procedure @1 not found", "336068752": "Exception not found", "336068754": "Parameter @1 in procedure @2 not found", "336068755": "<PERSON><PERSON> @1 not found", "336068759": "Character set @1 not found", "336068760": "Collation @1 not found", "336068763": "Role @1 not found", "336068767": "Name longer than database column size", "336068784": "column @1 does not exist in table/view @2", "336068796": "SQL role @1 does not exist", "336068797": "User @1 has no grant admin option on SQL role @2", "336068798": "User @1 is not a member of SQL role @2", "336068799": "@1 is not the owner of SQL role @2", "336068800": "@1 is a SQL role and not a user", "336068801": "User name @1 could not be used for SQL role", "336068802": "SQL role @1 already exists", "336068803": "Keyword @1 can not be used as a SQL role name", "336068804": "SQL roles are not supported in on older versions of the database. A backup and restore of the database is required", "336068812": "Cannot rename domain @1 to @2. A domain with that name already exists", "336068813": "Cannot rename column @1 to @2.A column with that name already exists in table @3", "336068814": "Column @1 from table @2 is referenced in @3", "336068815": "Cannot change datatype for column @1.Changing datatype is not supported for BLOB or ARRAY columns", "336068816": "New size specified for column @1 must be at least @2 characters", "336068817": "Cannot change datatype for @1.Conversion from base type @2 to @3 is not supported", "336068818": "Cannot change datatype for column @1 from a character type to a non-character type", "336068820": "Zero length identifiers are not allowed", "336068822": "Sequence @1 not found", "336068829": "Maximum number of collations per character set exceeded", "336068830": "Invalid collation attributes", "336068840": "@1 cannot reference @2", "336068843": "Collation @1 is used in table @2 (field name @3) and cannot be dropped", "336068844": "Collation @1 is used in domain @2 and cannot be dropped", "336068845": "Cannot delete system collation", "336068846": "Cannot delete default collation of CHARACTER SET @1", "336068849": "Table @1 not found", "336068851": "Collation @1 is used in procedure @2 (parameter name @3) and cannot be dropped", "336068852": "New scale specified for column @1 must be at most @2", "336068853": "New precision specified for column @1 must be at least @2", "336068855": "Warning: @1 on @2 is not granted to @3.", "336068856": "Feature '@1' is not supported in ODS @2.@3", "336068857": "Cannot add or remove COMPUTED from column @1", "336068858": "Password should not be empty string", "336068859": "Index @1 already exists", "336068864": "Package @1 not found", "336068865": "<PERSON><PERSON><PERSON> @1 not found", "336068866": "Cannot ALTER or DROP system procedure @1", "336068867": "Cannot ALTER or DROP system trigger @1", "336068868": "Cannot ALTER or DROP system function @1", "336068869": "Invalid DDL statement for procedure @1", "336068870": "Invalid DDL statement for trigger @1", "336068871": "Function @1 has not been defined on the package body @2", "336068872": "Procedure @1 has not been defined on the package body @2", "336068873": "Function @1 has a signature mismatch on package body @2", "336068874": "Procedure @1 has a signature mismatch on package body @2", "336068875": "Default values for parameters are not allowed in the definition of a previously declared packaged procedure @1.@2", "336068877": "Package body @1 already exists", "336068878": "Invalid DDL statement for function @1", "336068879": "Cannot alter new style function @1 with ALTER EXTERNAL FUNCTION. Use ALTER FUNCTION instead.", "336068886": "Parameter @1 in function @2 not found", "336068887": "Parameter @1 of routine @2 not found", "336068888": "Parameter @1 of routine @2 is ambiguous (found in both procedures and functions). Use a specifier keyword.", "336068889": "Collation @1 is used in function @2 (parameter name @3) and cannot be dropped", "336068890": "Domain @1 is used in function @2 (parameter name @3) and cannot be dropped", "336068891": "ALTER USER requires at least one clause to be specified", "336068894": "Duplicate @1 @2", "336068895": "System @1 @2 cannot be modified", "336068896": "INCREMENT BY 0 is an illegal option for sequence @1", "336068897": "Can’t use @1 in FOREIGN KEY constraint", "336068898": "Default values for parameters are not allowed in the definition of a previously declared packaged function @1.@2", "336068900": "role @1 can not be granted to role @2", "336068904": "INCREMENT BY 0 is an illegal option for identity column @1 of table @2", "336068907": "no @1 privilege with grant option on DDL @2", "336068908": "no @1 privilege with grant option on object @2", "336068909": "Function @1 does not exist", "336068910": "Procedure @1 does not exist", "336068911": "Package @1 does not exist", "336068912": "Trigger @1 does not exist", "336068913": "View @1 does not exist", "336068914": "Table @1 does not exist", "336068915": "Exception @1 does not exist", "336068916": "Generator/Sequence @1 does not exist", "336068917": "Field @1 of table @2 does not exist", "336330753": "Found unknown switch", "336330754": "Page size parameter missing", "336330755": "Page size specified (@1) greater than limit (16384 bytes)", "336330756": "Redirect location for output is not specified", "336330757": "Conflicting switches for backup/restore", "336330758": "Device type @1 not known", "336330759": "Protection is not there yet", "336330760": "Page size is allowed only on restore or create", "336330761": "Multiple sources or destinations specified", "336330762": "Requires both input and output filenames", "336330763": "Input and output have the same name. Disallowed", "336330764": "Expected page size, encountered \"@1\"", "336330765": "REPLACE specified, but the first file @1 is a database", "336330766": "Database @1 already exists.To replace it, use the -REP switch", "336330767": "Device type not specified", "336330772": "Gds_$blob_info failed", "336330773": "Do not understand BLOB INFO item @1", "336330774": "Gds_$get_segment failed", "336330775": "Gds_$close_blob failed", "336330776": "Gds_$open_blob failed", "336330777": "Failed in put_blr_gen_id", "336330778": "Data type @1 not understood", "336330779": "Gds_$compile_request failed", "336330780": "Gds_$start_request failed", "336330781": "gds_$receive failed", "336330782": "Gds_$release_request failed", "336330783": "gds_$database_info failed", "336330784": "Expected database description record", "336330785": "Failed to create database @1", "336330786": "RESTORE: decompression length error", "336330787": "Cannot find table @1", "336330788": "Cannot find column for BLOB", "336330789": "Gds_$create_blob failed", "336330790": "Gds_$put_segment failed", "336330791": "Expected record length", "336330792": "Wrong length record, expected @1 encountered @2", "336330793": "Expected data attribute", "336330794": "Failed in store_blr_gen_id", "336330795": "Do not recognize record type @1", "336330796": "Expected backup version 1..8. Found @1", "336330797": "Expected backup description record", "336330798": "String truncated", "336330799": "warning -- record could not be restored", "336330800": "Gds_$send failed", "336330801": "No table name for data", "336330802": "Unexpected end of file on backup file", "336330803": "Database format @1 is too old to restore to", "336330804": "Array dimension for column @1 is invalid", "336330807": "Expected XDR record length", "336330817": "Cannot open backup file @1", "336330818": "Cannot open status and error output file @1", "336330934": "Blocking factor parameter missing", "336330935": "Expected blocking factor, encountered \"@1\"", "336330936": "A blocking factor may not be used in conjunction with device CT", "336330940": "User name parameter missing", "336330941": "Password parameter missing", "336330952": "missing parameter for the number of bytes to be skipped", "336330953": "Expected number of bytes to be skipped, encountered \"@1\"", "336330965": "Character set", "336330967": "Collation", "336330972": "Unexpected I/O error while reading from backup file", "336330973": "Unexpected I/O error while writing to backup file", "336330985": "Could not drop database @1 (database might be in use)", "336330990": "System memory exhausted", "336331002": "SQL role", "336331005": "SQL role parameter missing", "336331010": "Page buffers parameter missing", "336331011": "Expected page buffers, encountered \"@1\"", "336331012": "Page buffers is allowed only on restore or create", "336331014": "Size specification either missing or incorrect for file @1", "336331015": "File @1 out of sequence", "336331016": "Can't join - one of the files missing", "336331017": "standard input is not supported when using join operation", "336331018": "Standard output is not supported when using split operation", "336331019": "Backup file @1 might be corrupt", "336331020": "Database file specification missing", "336331021": "Can't write a header record to file @1", "336331022": "Free disk space exhausted", "336331023": "File size given (@1) is less than minimum allowed (@2)", "336331025": "Service name parameter missing", "336331026": "Cannot restore over current database, must be SYSD<PERSON> or owner of the existing database", "336331031": "\"read_only\" or \"read_write\" required", "336331033": "Just data ignore all constraints etc.", "336331034": "Restoring data only ignoring foreign key, unique, not null & other constraints", "336397205": "ODS versions before ODS@1 are not supported", "336397206": "Table @1 does not exist", "336397207": "View @1 does not exist", "336397208": "At line @1, column @2", "336397209": "At unknown line and column", "336397210": "Column @1 cannot be repeated in @2 statement", "336397211": "Too many values ( more than @1) in member list to match against", "336397212": "Array and BLOB data types not allowed in computed field", "336397213": "Implicit domain name @1 not allowed in user created domain", "336397214": "Scalar operator used on field @1 which is not an array", "336397215": "Cannot sort on more than 255 items", "336397216": "Cannot group on more than 255 items", "336397217": "Cannot include the same field (@1.@2) twice in the ORDER BY clause with conflicting sorting options", "336397218": "Column list from derived table @1 has more columns than the number of items in its SELECT statement", "336397219": "Column list from derived table @1 has less columns than the number of items in its SELECT statement", "336397220": "No column name specified for column number @1 in derived table @2", "336397221": "Column @1 was specified multiple times for derived table @2", "336397222": "Internal dsql error: alias type expected by pass1_expand_select_node", "336397223": "Internal dsql error: alias type expected by pass1_field", "336397224": "Internal dsql error: column position out of range in pass1_union_auto_cast", "336397225": "Recursive CTE member (@1) can refer itself only in FROM clause", "336397226": "CTE '@1' has cyclic dependencies", "336397227": "Recursive member of CTE can't be member of an outer join", "336397228": "Recursive member of CTE can't reference itself more than once", "336397229": "Recursive CTE (@1) must be an UNION", "336397230": "CTE '@1' defined non-recursive member after recursive", "336397231": "Recursive member of CTE '@1' has @2 clause", "336397232": "Recursive members of CTE (@1) must be linked with another members via UNION ALL", "336397233": "Non-recursive member is missing in CTE '@1'", "336397234": "WITH clause can't be nested", "336397235": "Column @1 appears more than once in USING clause", "336397236": "Feature is not supported in dialect @1", "336397237": "CTE \"@1\" is not used in query", "336397238": "column @1 appears more than once in ALTER VIEW", "336397239": "@1 is not supported inside IN AUTONOMOUS TRANSACTION block", "336397240": "Unknown node type @1 in dsql/GEN_expr", "336397241": "Argument for @1 in dialect 1 must be string or numeric", "336397242": "Argument for @1 in dialect 3 must be numeric", "336397243": "Strings cannot be added to or subtracted from DATE or TIME types", "336397244": "Invalid data type for subtraction involving DATE, TIME or TIMESTAMP types", "336397245": "Adding two DATE values or two TIME values is not allowed", "336397246": "DATE value cannot be subtracted from the provided data type", "336397247": "Strings cannot be added or subtracted in dialect 3", "336397248": "Invalid data type for addition or subtraction in dialect 3", "336397249": "Invalid data type for multiplication in dialect 1", "336397250": "Strings cannot be multiplied in dialect 3", "336397251": "Invalid data type for multiplication in dialect 3", "336397252": "Division in dialect 1 must be between numeric data types", "336397253": "Strings cannot be divided in dialect 3", "336397254": "Invalid data type for division in dialect 3", "336397255": "Strings cannot be negated (applied the minus operator) in dialect 3", "336397256": "Invalid data type for negation (minus operator)", "336397257": "Cannot have more than 255 items in DISTINCT / UNION DISTINCT list", "336397258": "ALTER CHARACTER SET @1 failed", "336397259": "COMMENT ON @1 failed", "336397260": "CREATE FUNCTION @1 failed", "336397261": "ALTER FUNCTION @1 failed", "336397262": "CREATE OR ALTER FUNCTION @1 failed", "336397263": "DROP FUNCTION @1 failed", "336397264": "RECREATE FUNCTION @1 failed", "336397265": "CREATE PROCEDURE @1 failed", "336397266": "ALTER PROCEDURE @1 failed", "336397267": "CREATE OR ALTER PROCEDURE @1 failed", "336397268": "DROP PROCEDURE @1 failed", "336397269": "RECREATE PROCEDURE @1 failed", "336397270": "CREATE TRIGGER @1 failed", "336397271": "ALTER TRIGGER @1 failed", "336397272": "CREATE OR ALTER TRIGGER @1 failed", "336397273": "DROP TRIGGER @1 failed", "336397274": "RECREATE TRIGGER @1 failed", "336397275": "CREATE COLLATION @1 failed", "336397276": "DROP COLLATION @1 failed", "336397277": "CREATE DOMAIN @1 failed", "336397278": "ALTER DOMAIN @1 failed", "336397279": "DROP DOMAIN @1 failed", "336397280": "CREATE EXCEPTION @1 failed", "336397281": "ALTER EXCEPTION @1 failed", "336397282": "CREATE OR ALTER EXCEPTION @1 failed", "336397283": "RECREATE EXCEPTION @1 failed", "336397284": "DROP EXCEPTION @1 failed", "336397285": "CREATE SEQUENCE @1 failed", "336397286": "CREATE TABLE @1 failed", "336397287": "ALTER TABLE @1 failed", "336397288": "DROP TABLE @1 failed", "336397289": "RECREATE TABLE @1 failed", "336397290": "CREATE PACKAGE @1 failed", "336397291": "ALTER PACKAGE @1 failed", "336397292": "CREATE OR ALTER PACKAGE @1 failed", "336397293": "DROP PACKAGE @1 failed", "336397294": "RECREATE PACKAGE @1 failed", "336397295": "CREATE PACKAGE BODY @1 failed", "336397296": "DROP PACKAGE BODY @1 failed", "336397297": "RECREATE PACKAGE BODY @1 failed", "336397298": "CREATE VIEW @1 failed", "336397299": "ALTER VIEW @1 failed", "336397300": "CREATE OR ALTER VIEW @1 failed", "336397301": "RECREATE VIEW @1 failed", "336397302": "DROP VIEW @1 failed", "336397303": "DROP SEQUENCE @1 failed", "336397304": "RECREATE SEQUENCE @1 failed", "336397305": "DROP INDEX @1 failed", "336397306": "DROP FILTER @1 failed", "336397307": "DROP SHADOW @1 failed", "336397308": "DROP ROLE @1 failed", "336397309": "DROP USER @1 failed", "336397310": "CREATE ROLE @1 failed", "336397311": "ALTER ROLE @1 failed", "336397312": "ALTER INDEX @1 failed", "336397313": "ALTER DATABASE failed", "336397314": "CREATE SHADOW @1 failed", "336397315": "DECLARE FILTER @1 failed", "336397316": "CREATE INDEX @1 failed", "336397317": "CREATE USER @1 failed", "336397318": "ALTER USER @1 failed", "336397319": "GRANT failed", "336397320": "REVOKE failed", "336397321": "Recursive member of CTE cannot use aggregate or window function", "336397322": "@2 MAPPING @1 failed", "336397323": "ALTER SEQUENCE @1 failed", "336397324": "CREATE GENERATOR @1 failed", "336397325": "SET GENERATOR @1 failed", "336397326": "WITH LOCK can be used only with a single physical table", "336397327": "FIRST/SKIP cannot be used with OFFSET/FETCH or ROWS", "336397328": "WITH LOCK cannot be used with aggregates", "336397329": "WITH LOCK cannot be used with @1", "336397330": "Number of arguments (@1) exceeds the maximum (@2) number of EXCEPTION USING arguments", "336397331": "String literal with @1 bytes exceeds the maximum length of @2 bytes", "336397332": "String literal with @1 characters exceeds the maximum length of @2 characters for the @3 character set", "336397333": "Too many BEGIN…​END nesting. Maximum level is @1", "336397334": "RECREATE USER @1 failed", "336723983": "Unable to open database", "336723984": "Error in switch specifications", "336723985": "No operation specified", "336723986": "No user name specified", "336723987": "Add record error", "336723988": "Modify record error", "336723989": "Find / modify record error", "336723990": "Record not found for user: @1", "336723991": "Delete record error", "336723992": "Find / delete record error", "336723996": "Find / display record error", "336723997": "Invalid parameter, no switch defined", "336723998": "Operation already specified", "336723999": "Password already specified", "336724000": "Uid already specified", "336724001": "Gid already specified", "336724002": "Project already specified", "336724003": "Organization already specified", "336724004": "First name already specified", "336724005": "Middle name already specified", "336724006": "Last name already specified", "336724008": "Invalid switch specified", "336724009": "Ambiguous switch specified", "336724010": "No operation specified for parameters", "336724011": "No parameters allowed for this operation", "336724012": "Incompatible switches specified", "336724044": "Invalid user name (maximum 31 bytes allowed)", "336724045": "Warning - maximum 8 significant bytes of password used", "336724046": "Database already specified", "336724047": "Database administrator name already specified", "336724048": "Database administrator password already specified", "336724049": "SQL role name already specified", "336920577": "Found unknown switch", "336920578": "Please retry, giving a database name", "336920579": "Wrong ODS version, expected @1, encountered @2", "336920580": "Unexpected end of database file", "336920605": "Can't open database file @1", "336920606": "Can't read a database page", "336920607": "System memory exhausted", "336986113": "Wrong value for access mode", "336986114": "Wrong value for write mode", "336986115": "Wrong value for reserve space", "336986116": "Unknown tag (@1) in info_svr_db_info block after isc_svc_query()", "336986117": "Unknown tag (@1) in isc_svc_query() results", "336986118": "Unknown switch \"@1\""}