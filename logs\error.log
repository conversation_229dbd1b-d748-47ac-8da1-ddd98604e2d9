{"gdscode":335544569,"level":"error","message":"Error procesando factura SEA3464: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:08:26.196Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura aprobada: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:08:26.196Z"}
{"data":[{"field":"E","length":"N/A","value":1},{"field":"S","length":"N/A","value":1},{"field":"TIPO","length":3,"value":"FIA"},{"field":"BATCH","length":"N/A","value":18134},{"field":"ID_N","length":11,"value":"*********-4"},{"field":"FECHA","length":"N/A","value":"2025-05-13T00:00:00.000Z"},{"field":"TOTAL","length":"N/A","value":9565604.91},{"field":"USERNAME","length":6,"value":"SYSTEM"},{"field":"FECHA_HORA","length":20,"value":"20/6/2025, 7:12:55 a"},{"field":"OBSERV","length":31,"value":"Factura SEA3464 - GRUPO SAI SAS"},{"field":"BANCO","length":0,"value":""},{"field":"CHEQUE","length":0,"value":""},{"field":"DUEDATE","length":"N/A","value":"2025-05-13T00:00:00.000Z"},{"field":"LETRAS","length":104,"value":"NUEVE MILLONES QUINIENTOS SESENTA Y CINCO MIL SEISCIENTOS CUATRO PESOS CON NOVENTA Y UNO CENTAVOS. M/CTE"},{"field":"IDVEND","length":"N/A","value":1},{"field":"SHIPTO","length":"N/A","value":0},{"field":"EXPORTADA","length":1,"value":"N"},{"field":"ENTREGADO","length":1,"value":"N"},{"field":"REVISADO","length":1,"value":"N"},{"field":"REVISOR","length":"N/A","value":null},{"field":"FECHA_REVISION","length":"N/A","value":null},{"field":"IMPRESO","length":1,"value":"N"},{"field":"DOC_FISICO","length":"N/A","value":null},{"field":"CHEQUE_POSTF","length":5,"value":"false"},{"field":"FECHA_CHEQUE","length":"N/A","value":null},{"field":"PROYECTO","length":"N/A","value":null},{"field":"SALDO_DEUDA","length":"N/A","value":null},{"field":"SALDO_DEUDA_ABONO","length":"N/A","value":null},{"field":"PONUMBER","length":"N/A","value":null},{"field":"INTERES_IMPLICITO","length":1,"value":"N"},{"field":"DETALLE","length":0,"value":""},{"field":"FECHA_CONTAB_CONSIG","length":1,"value":"N"},{"field":"DETERIORO_ESFA","length":1,"value":"N"},{"field":"CONCEPTO_NOTAFE","length":"N/A","value":null},{"field":"ENVIADO","length":1,"value":"N"},{"field":"CUFE","length":"N/A","value":null},{"field":"SUBTOTAL","length":"N/A","value":8038323.45},{"field":"SALESTAX","length":"N/A","value":1527281.46},{"field":"IMPCONSUMO","length":"N/A","value":0},{"field":"TOTAL_REAL","length":"N/A","value":9565604.91},{"field":"FECHA_RESPUESTA_DIAN","length":"N/A","value":null},{"field":"ID_BINARIO","length":"N/A","value":null},{"field":"SIN_CRUCE","length":1,"value":"N"},{"field":"CUDS","length":"N/A","value":null},{"field":"COD_OPERACION","length":"N/A","value":null},{"field":"FORMAPAGO","length":"N/A","value":null},{"field":"ID_RES","length":"N/A","value":0},{"field":"FECHA_INICIO_PERIODO","length":"N/A","value":null},{"field":"FECHA_FIN_PERIODO","length":"N/A","value":null}],"error":"Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')","level":"error","message":"Error insertando en CARPROEN:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPROEN (E, S, TIPO, BATCH, ID_N, FECHA, TOTAL, USERNAME, FECHA_HORA, OBSERV, BANCO, CHEQUE, DUEDATE, LETRAS, IDVEND, SHIPTO, EXPORTADA, ENTREGADO, REVISADO, REVISOR, FECHA_REVISION, IMPRESO, DOC_FISICO, CHEQUE_POSTF, FECHA_CHEQUE, PROYECTO, SALDO_DEUDA, SALDO_DEUDA_ABONO, PONUMBER, INTERES_IMPLICITO, DETALLE, FECHA_CONTAB_CONSIG, DETERIORO_ESFA, CONCEPTO_NOTAFE, ENVIADO, CUFE, SUBTOTAL, SALESTAX, IMPCONSUMO, TOTAL_REAL, FECHA_RESPUESTA_DIAN, ID_BINARIO, SIN_CRUCE, CUDS, COD_OPERACION, FORMAPAGO, ID_RES, FECHA_INICIO_PERIODO, FECHA_FIN_PERIODO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T12:12:55.213Z"}
{"gdscode":*********,"gdsparams":["FK_CARPROEN_CUST","CARPROEN"],"level":"error","message":"Error procesando factura SEA3464: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')","service":"supabase-firebird-sync","stack":"Error: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:12:55.217Z"}
{"gdscode":*********,"gdsparams":["FK_CARPROEN_CUST","CARPROEN"],"level":"error","message":"Error procesando factura aprobada: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')","service":"supabase-firebird-sync","stack":"Error: Violation of FOREIGN KEY constraint \"FK_CARPROEN_CUST\" on table \"CARPROEN\", Foreign key reference target does not exist, Problematic key value is (\"ID_N\" = '*********-4')\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T12:12:55.217Z"}
{"data":[{"field":"TIPO","length":3,"value":"FIA"},{"field":"BATCH","length":"N/A","value":18134},{"field":"ID_N","length":9,"value":"*********"},{"field":"ACCT","length":"N/A","value":51950501},{"field":"E","length":"N/A","value":1},{"field":"S","length":"N/A","value":1},{"field":"CRUCE","length":0,"value":""},{"field":"INVC","length":7,"value":"SEA3464"},{"field":"FECHA","length":"N/A","value":"2025-06-06T00:00:00.000Z"},{"field":"DUEDATE","length":"N/A","value":"2025-06-06T00:00:00.000Z"},{"field":"DPTO","length":"N/A","value":0},{"field":"CCOST","length":"N/A","value":0},{"field":"ACTIVIDAD","length":0,"value":""},{"field":"DESCRIPCION","length":50,"value":"Registro de la comisión sobre ventas CS según fact"},{"field":"DIAS","length":"N/A","value":0},{"field":"DESTINO","length":"N/A","value":1},{"field":"TIPO_REF","length":"N/A","value":null},{"field":"REFERENCIA","length":"N/A","value":null},{"field":"TIPO_IMP","length":0,"value":""},{"field":"NRO_IMP","length":"N/A","value":0},{"field":"CONCEPTO_IMP","length":"N/A","value":0},{"field":"BANCO","length":"N/A","value":null},{"field":"CHEQUE","length":"N/A","value":null},{"field":"PROYECTO","length":0,"value":""},{"field":"CONCEPTO_PAGO","length":"N/A","value":null},{"field":"ID_TIPOCARTERA","length":"N/A","value":null},{"field":"INVC_ENTERO","length":"N/A","value":0},{"field":"CHEQUE_POSTF","length":5,"value":"false"},{"field":"FECHA_CHEQUE","length":"N/A","value":null},{"field":"SALDO","length":"N/A","value":-8038323.45},{"field":"CREDIT","length":"N/A","value":0},{"field":"TASA_CAMBIO","length":"N/A","value":0},{"field":"CREDITO_US","length":"N/A","value":0},{"field":"DEBITO_US","length":"N/A","value":0},{"field":"BASE","length":"N/A","value":8038323.45},{"field":"DEBIT","length":"N/A","value":8038323.45},{"field":"CUOTA","length":"N/A","value":1},{"field":"FECHA_CONSIG","length":"N/A","value":null},{"field":"FECHA_FACTURA","length":"N/A","value":"2025-05-13T00:00:00.000Z"},{"field":"MAYOR_VALOR","length":"N/A","value":null},{"field":"VALOR_IMPUESTO","length":"N/A","value":null},{"field":"IMPORT","length":1,"value":"N"},{"field":"COD_FLUJOEFE","length":"N/A","value":0},{"field":"IDVEND","length":"N/A","value":null},{"field":"PORC_TASA","length":"N/A","value":19},{"field":"TIEMPO_MESES","length":"N/A","value":null},{"field":"PAGO_DISP","length":1,"value":"N"},{"field":"REGISTROELECT","length":1,"value":"N"},{"field":"PORC_RETENCION","length":"N/A","value":null},{"field":"BASE_ELECTRONICA","length":"N/A","value":0}],"error":"Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","level":"error","message":"Error insertando en CARPRODE:","service":"supabase-firebird-sync","sql":"INSERT INTO CARPRODE (TIPO, BATCH, ID_N, ACCT, E, S, CRUCE, INVC, FECHA, DUEDATE, DPTO, CCOST, ACTIVIDAD, DESCRIPCION, DIAS, DESTINO, TIPO_REF, REFERENCIA, TIPO_IMP, NRO_IMP, CONCEPTO_IMP, BANCO, CHEQUE, PROYECTO, CONCEPTO_PAGO, ID_TIPOCARTERA, INVC_ENTERO, CHEQUE_POSTF, FECHA_CHEQUE, SALDO, CREDIT, TASA_CAMBIO, CREDITO_US, DEBITO_US, BASE, DEBIT, CUOTA, FECHA_CONSIG, FECHA_FACTURA, MAYOR_VALOR, VALOR_IMPUESTO, IMPORT, COD_FLUJOEFE, IDVEND, PORC_TASA, TIEMPO_MESES, PAGO_DISP, REGISTROELECT, PORC_RETENCION, BASE_ELECTRONICA) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","timestamp":"2025-06-20T13:19:40.634Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura SEA3464: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T13:19:40.670Z"}
{"gdscode":335544569,"level":"error","message":"Error procesando factura aprobada: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation","service":"supabase-firebird-sync","stack":"Error: Dynamic SQL Error, SQL error code = -303, Arithmetic exception, numeric overflow, or string truncation, string right truncation\n    at doCallback (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\callback.js:23:21)\n    at D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:172:21\n    at loop (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:232:29)\n    at parseOpResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:515:29)\n    at decodeResponse (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:237:24)\n    at Socket.<anonymous> (D:\\serverN8N\\Webs\\ServicioSAIDB\\node_modules\\node-firebird\\lib\\wire\\connection.js:149:13)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-06-20T13:19:40.670Z"}
