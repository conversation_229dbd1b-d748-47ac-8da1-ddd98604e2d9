language: node_js

node_js:
  - lts/*
  - v10  

notifications:
  email:
  - <EMAIL>  

env:
  - FIREBIRD_DATA="/var/lib/firebird/2.5/data"

before_install:
  - sudo apt-get update -qq
  - sudo DEBIAN_FRONTEND=noninteractive apt-get install -qq firebird2.5-superclassic 
  - sudo gsec -modify SYSDBA -pw masterkey

script:
  - nyc npm test
  
after_script:
  - nyc report --reporter=text-lcov | coveralls
